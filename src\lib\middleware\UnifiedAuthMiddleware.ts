/**
 * Unified Authentication Middleware
 * Consolidates all authentication logic into a single, reusable system
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, JWTPayload } from '@/lib/jwt';
import { extractTokenFromHeader } from '@/lib/jwt';

// Types and Interfaces
export interface AuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
  statusCode?: number;
}

export interface AuthUser {
  userId: string;
  accountType: 'user' | 'admin' | 'business';
  email?: string;
  role?: string;
  isRestricted?: boolean;
}

export type AuthRequirement = 'any' | 'user' | 'admin' | 'business' | 'admin_or_business';

/**
 * Unified Authentication Middleware Class
 */
export class UnifiedAuthMiddleware {
  private static instance: UnifiedAuthMiddleware;

  private constructor() {}

  public static getInstance(): UnifiedAuthMiddleware {
    if (!UnifiedAuthMiddleware.instance) {
      UnifiedAuthMiddleware.instance = new UnifiedAuthMiddleware();
    }
    return UnifiedAuthMiddleware.instance;
  }

  /**
   * Verify user authentication (any authenticated user)
   */
  public async verifyUser(request: NextRequest): Promise<AuthResult> {
    return this.verifyAuth(request, 'any');
  }

  /**
   * Verify business user authentication
   */
  public async verifyBusiness(request: NextRequest): Promise<AuthResult> {
    return this.verifyAuth(request, 'business');
  }

  /**
   * Verify admin user authentication
   */
  public async verifyAdmin(request: NextRequest): Promise<AuthResult> {
    return this.verifyAuth(request, 'admin');
  }

  /**
   * Verify admin or business user authentication
   */
  public async verifyAdminOrBusiness(request: NextRequest): Promise<AuthResult> {
    return this.verifyAuth(request, 'admin_or_business');
  }

  /**
   * Verify authentication with specific requirements
   */
  public async verifyAuth(request: NextRequest, requirement: AuthRequirement = 'any'): Promise<AuthResult> {
    try {
      // Extract token from request
      const token = this.extractToken(request);
      if (!token) {
        return {
          success: false,
          error: 'No authentication token provided',
          statusCode: 401
        };
      }

      // Parse and verify token
      const authData = await this.parseAndVerifyToken(token);
      if (!authData) {
        return {
          success: false,
          error: 'Invalid or expired authentication token',
          statusCode: 401
        };
      }

      // Check if user meets requirements
      const requirementCheck = this.checkRequirement(authData, requirement);
      if (!requirementCheck.success) {
        return requirementCheck;
      }

      return {
        success: true,
        user: authData
      };

    } catch (error) {
      console.error('Authentication verification error:', error);
      return {
        success: false,
        error: 'Authentication verification failed',
        statusCode: 500
      };
    }
  }

  /**
   * Create authentication response for failed auth
   */
  public createAuthErrorResponse(authResult: AuthResult): NextResponse {
    const statusCode = authResult.statusCode || 401;
    const message = authResult.error || 'Authentication failed';

    return NextResponse.json({
      error: message,
      authenticated: false
    }, { status: statusCode });
  }

  /**
   * Create middleware wrapper for API routes
   */
  public withAuth(requirement: AuthRequirement = 'any') {
    return (handler: (request: NextRequest, authUser: AuthUser) => Promise<NextResponse>) => {
      return async (request: NextRequest): Promise<NextResponse> => {
        const authResult = await this.verifyAuth(request, requirement);
        
        if (!authResult.success) {
          return this.createAuthErrorResponse(authResult);
        }

        return handler(request, authResult.user!);
      };
    };
  }

  /**
   * Extract authentication token from request
   */
  private extractToken(request: NextRequest): string | null {
    // First try Authorization header (preferred for API calls)
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      const token = extractTokenFromHeader(authHeader);
      if (token) {
        return token;
      }
    }

    // Fallback to cookie-based authentication
    const cookieHeader = request.headers.get('cookie');
    if (!cookieHeader) {
      return null;
    }

    const cookies = cookieHeader.split(';');
    let authCookie = cookies.find(cookie => cookie.trim().startsWith('auth_token='));

    if (!authCookie) {
      return null;
    }

    const cookieValue = authCookie.split('=')[1];
    if (!cookieValue) {
      return null;
    }

    // Try to decode if it's URI encoded
    try {
      return decodeURIComponent(cookieValue);
    } catch {
      return cookieValue;
    }
  }

  /**
   * Parse and verify authentication token
   */
  private async parseAndVerifyToken(token: string): Promise<AuthUser | null> {
    try {
      let userId: string | null = null;
      let accountType: string | null = null;
      let email: string | undefined = undefined;

      // Check if it's a JWT token or old format
      if (token.includes('.')) {
        // JWT token format
        try {
          const payload = verifyToken(token);
          if (!payload) {
            return null;
          }
          
          userId = payload.userId?.toString() || null;
          accountType = payload.accountType || null;
          email = payload.email || undefined;
        } catch (error) {
          console.error('Error verifying JWT token:', error);
          return null;
        }
      } else {
        // Old format fallback (userId_accountType or userId:accountType:email)
        if (token.includes(':')) {
          // New simple format: userId:accountType:email
          const parts = token.split(':');
          if (parts.length >= 2) {
            userId = parts[0];
            accountType = parts[1];
            email = parts[2] || undefined;
          }
        } else if (token.includes('_')) {
          // Legacy format: userId_accountType
          const parts = token.split('_');
          if (parts.length === 2) {
            userId = parts[0];
            accountType = parts[1];
          }
        }
      }

      if (!userId || !accountType) {
        return null;
      }

      // Validate account type
      if (!['user', 'admin', 'business'].includes(accountType)) {
        return null;
      }

      return {
        userId,
        accountType: accountType as 'user' | 'admin' | 'business',
        email
      };

    } catch (error) {
      console.error('Token parsing error:', error);
      return null;
    }
  }

  /**
   * Check if user meets authentication requirements
   */
  private checkRequirement(user: AuthUser, requirement: AuthRequirement): AuthResult {
    switch (requirement) {
      case 'any':
        return { success: true };

      case 'user':
        if (user.accountType !== 'user') {
          return {
            success: false,
            error: 'User account required',
            statusCode: 403
          };
        }
        return { success: true };

      case 'admin':
        if (user.accountType !== 'admin') {
          return {
            success: false,
            error: 'Admin access required',
            statusCode: 403
          };
        }
        return { success: true };

      case 'business':
        if (user.accountType !== 'business') {
          return {
            success: false,
            error: 'Business account required',
            statusCode: 403
          };
        }
        return { success: true };

      case 'admin_or_business':
        if (!['admin', 'business'].includes(user.accountType)) {
          return {
            success: false,
            error: 'Admin or business account required',
            statusCode: 403
          };
        }
        return { success: true };

      default:
        return {
          success: false,
          error: 'Invalid authentication requirement',
          statusCode: 500
        };
    }
  }

  /**
   * Legacy compatibility methods
   */
  public async verifySecureAuth(request: NextRequest): Promise<{ userId: string; accountType: string; email?: string } | null> {
    const result = await this.verifyAuth(request, 'any');
    if (!result.success || !result.user) {
      return null;
    }

    return {
      userId: result.user.userId,
      accountType: result.user.accountType,
      email: result.user.email
    };
  }

  public async getAuthTokenFromRequest(request: NextRequest): Promise<string | null> {
    return this.extractToken(request);
  }

  public async parseAuthToken(token: string): Promise<{ userId: string; accountType: string; email?: string } | null> {
    const user = await this.parseAndVerifyToken(token);
    if (!user) {
      return null;
    }

    return {
      userId: user.userId,
      accountType: user.accountType,
      email: user.email
    };
  }
}

// Export singleton instance and convenience functions
export const authMiddleware = UnifiedAuthMiddleware.getInstance();

// Convenience functions for backward compatibility
export const verifySecureAuth = (request: NextRequest) => authMiddleware.verifySecureAuth(request);
export const getAuthTokenFromRequest = (request: NextRequest) => authMiddleware.getAuthTokenFromRequest(request);
export const parseAuthToken = (token: string) => authMiddleware.parseAuthToken(token);

// New unified functions
export const verifyUser = (request: NextRequest) => authMiddleware.verifyUser(request);
export const verifyAdmin = (request: NextRequest) => authMiddleware.verifyAdmin(request);
export const verifyBusiness = (request: NextRequest) => authMiddleware.verifyBusiness(request);
export const verifyAdminOrBusiness = (request: NextRequest) => authMiddleware.verifyAdminOrBusiness(request);

// Middleware wrapper for API routes
export const withAuth = (requirement: AuthRequirement = 'any') => authMiddleware.withAuth(requirement);

// Helper function to create standardized auth error responses
export const createAuthErrorResponse = (message: string, statusCode: number = 401) => {
  return NextResponse.json({
    error: message,
    authenticated: false
  }, { status: statusCode });
};
