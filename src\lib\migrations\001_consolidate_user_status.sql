-- Migration 001: Consolidate User Status Fields
-- Objective: Merge status and restriction_status columns in users table
-- Date: 2025-07-14
-- Author: Database Consolidation Project

-- ============================================================================
-- FORWARD MIGRATION
-- ============================================================================

-- Step 1: Create backup table for safety (skip if exists)
CREATE TABLE IF NOT EXISTS users_backup_001 AS SELECT * FROM users;

-- Step 2: Check current state and show what we're working with
SELECT
  'Current Users Table Structure' as info,
  COUNT(*) as total_users
FROM users;

-- Step 3: Check if restriction_status column exists
-- This will help us determine if consolidation is needed
SELECT
  'Column Check' as check_type,
  column_name,
  data_type,
  column_type
FROM information_schema.columns
WHERE table_schema = DATABASE()
  AND table_name = 'users'
  AND column_name IN ('status', 'restriction_status')
ORDER BY column_name;

-- Step 4: Show current data distribution
SELECT
  'Current Data Distribution' as report_type,
  status,
  COALESCE(new_status, 'not_set') as new_status,
  COUNT(*) as count
FROM users
GROUP BY status, new_status;

-- Step 5: Check if new_status column already exists
SELECT
  'New Status Column Check' as check_type,
  CASE
    WHEN COUNT(*) > 0 THEN 'new_status column exists'
    ELSE 'new_status column missing'
  END as status
FROM information_schema.columns
WHERE table_schema = DATABASE()
  AND table_name = 'users'
  AND column_name = 'new_status';

-- Step 6: Ensure new_status column exists with correct enum values
-- This will either add the column or modify it to have the correct enum values
ALTER TABLE users MODIFY COLUMN new_status ENUM(
  'active',
  'inactive',
  'suspended',
  'restricted',
  'banned'
) DEFAULT 'active';

-- Step 7: Migrate existing data from status to new_status
-- Only update if new_status is NULL or 'active' (default)
UPDATE users SET new_status =
  CASE
    WHEN status = 'suspended' THEN 'suspended'
    WHEN status = 'restricted' THEN 'restricted'
    WHEN status = 'inactive' THEN 'inactive'
    ELSE 'active'
  END
WHERE new_status IS NULL OR new_status = 'active';

-- Step 8: Verify data migration
SELECT
  'Data Migration Verification' as check_type,
  status as old_status,
  new_status,
  COUNT(*) as count
FROM users
GROUP BY status, new_status
ORDER BY status, new_status;

-- Step 9: Check for any NULL values in new_status
SELECT
  'NULL Check' as check_type,
  COUNT(*) as null_count
FROM users
WHERE new_status IS NULL;

-- Step 10: Replace old status column with new consolidated status
-- First rename old status column
ALTER TABLE users CHANGE COLUMN status old_status ENUM('active','inactive','suspended','restricted') DEFAULT 'active';

-- Then rename new_status to status with expanded enum
ALTER TABLE users CHANGE COLUMN new_status status ENUM(
  'active',
  'inactive',
  'suspended',
  'restricted',
  'banned'
) DEFAULT 'active';

-- Finally drop the old status column
ALTER TABLE users DROP COLUMN old_status;

-- Step 7: Update migration history
INSERT INTO migration_history (migration_name, executed_at, success, error_message) 
VALUES ('001_consolidate_user_status', NOW(), 1, NULL);

-- ============================================================================
-- VALIDATION QUERIES
-- ============================================================================

-- Verify migration success
SELECT 
  'Post-Migration Status Distribution' as report,
  status,
  COUNT(*) as user_count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users), 2) as percentage
FROM users 
GROUP BY status
ORDER BY user_count DESC;

-- Check for any data anomalies
SELECT 
  user_id,
  email,
  status,
  created_at
FROM users 
WHERE status NOT IN ('active', 'inactive', 'suspended', 'restricted', 'banned')
LIMIT 10;

-- ============================================================================
-- ROLLBACK SCRIPT (Run only if migration needs to be reversed)
-- ============================================================================

/*
-- ROLLBACK INSTRUCTIONS:
-- 1. Uncomment the following section
-- 2. Execute the rollback script
-- 3. Verify data integrity

-- Rollback Step 1: Add back the old columns
ALTER TABLE users ADD COLUMN restriction_status ENUM('none','restricted','suspended') DEFAULT 'none' AFTER status;
ALTER TABLE users CHANGE COLUMN status new_status ENUM('active','inactive','suspended','restricted','banned') DEFAULT 'active';
ALTER TABLE users ADD COLUMN status ENUM('active','inactive','suspended','restricted') DEFAULT 'active' AFTER new_status;

-- Rollback Step 2: Restore original data logic from backup
UPDATE users u
JOIN users_backup_001 b ON u.user_id = b.user_id
SET 
  u.status = b.status,
  u.restriction_status = b.restriction_status;

-- Rollback Step 3: Drop the new column
ALTER TABLE users DROP COLUMN new_status;

-- Rollback Step 4: Verify rollback
SELECT 
  'Rollback Verification' as report,
  status,
  restriction_status,
  COUNT(*) as count
FROM users 
GROUP BY status, restriction_status;

-- Rollback Step 5: Update migration history
UPDATE migration_history 
SET success = 0, error_message = 'Migration rolled back manually'
WHERE migration_name = '001_consolidate_user_status';

-- Rollback Step 6: Drop backup table (optional)
-- DROP TABLE users_backup_001;
*/

-- ============================================================================
-- CLEANUP (Run after successful migration and testing)
-- ============================================================================

-- Drop backup table after confirming migration success
-- Uncomment the following line after thorough testing:
-- DROP TABLE users_backup_001;
