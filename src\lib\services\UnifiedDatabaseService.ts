/**
 * Unified Database Service
 * Consolidates all database connection logic, health checks, and table management
 */

import { ensureConnection, getHealthStatus, getDatabaseStatus } from '@/lib/services/UnifiedDatabaseService';

// Types and Interfaces
export interface DatabaseHealth {
  isConnected: boolean;
  poolStats: {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    queuedRequests: number;
  };
  responseTime: number;
  errors: string[];
}

export interface TableSchema {
  name: string;
  createSQL: string;
  requiredColumns?: string[];
}

export interface DatabaseStatus {
  connected: boolean;
  tablesExist: boolean;
  missingTables: string[];
  errors: string[];
}

/**
 * Unified Database Service Class
 */
export class UnifiedDatabaseService {
  private static instance: UnifiedDatabaseService;
  private healthCache: { data: DatabaseHealth; timestamp: number } | null = null;
  private readonly HEALTH_CACHE_TTL = 30000; // 30 seconds

  private constructor() {}

  public static getInstance(): UnifiedDatabaseService {
    if (!UnifiedDatabaseService.instance) {
      UnifiedDatabaseService.instance = new UnifiedDatabaseService();
    }
    return UnifiedDatabaseService.instance;
  }

  /**
   * Ensure database connection is available
   */
  public async ensureConnection(): Promise<boolean> {
    try {
      return await ensureConnection();
    } catch (error) {
      console.error('Database connection check failed:', error);
      return false;
    }
  }

  /**
   * Get comprehensive database health information
   */
  public async getHealthStatus(): Promise<DatabaseHealth> {
    // Check cache first
    if (this.healthCache && (Date.now() - this.healthCache.timestamp) < this.HEALTH_CACHE_TTL) {
      return this.healthCache.data;
    }

    try {
      const health = await getHealthStatus();
      
      // Cache the result
      this.healthCache = {
        data: health,
        timestamp: Date.now()
      };

      return health;
    } catch (error) {
      console.error('Database health check failed:', error);
      return {
        isConnected: false,
        poolStats: {
          totalConnections: 0,
          activeConnections: 0,
          idleConnections: 0,
          queuedRequests: 0
        },
        responseTime: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Check if a specific table exists
   */
  public async tableExists(tableName: string): Promise<boolean> {
    try {
      const result = await query(
        `SELECT COUNT(*) as count FROM information_schema.tables 
         WHERE table_schema = DATABASE() AND table_name = ?`,
        [tableName]
      ) as any[];

      return result && result.length > 0 && result[0].count > 0;
    } catch (error) {
      console.error(`Error checking if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Check if multiple tables exist
   */
  public async tablesExist(tableNames: string[]): Promise<{ [tableName: string]: boolean }> {
    const results: { [tableName: string]: boolean } = {};

    try {
      const placeholders = tableNames.map(() => '?').join(',');
      const result = await query(
        `SELECT table_name FROM information_schema.tables 
         WHERE table_schema = DATABASE() AND table_name IN (${placeholders})`,
        tableNames
      ) as any[];

      // Initialize all tables as not existing
      tableNames.forEach(name => {
        results[name] = false;
      });

      // Mark existing tables as true
      if (result && Array.isArray(result)) {
        result.forEach((row: any) => {
          const tableName = row.table_name || row.TABLE_NAME;
          if (tableName) {
            results[tableName] = true;
          }
        });
      }

    } catch (error) {
      console.error('Error checking multiple tables existence:', error);
      // Return all false if query fails
      tableNames.forEach(name => {
        results[name] = false;
      });
    }

    return results;
  }

  /**
   * Ensure a table exists with the given schema
   */
  public async ensureTable(schema: TableSchema): Promise<boolean> {
    try {
      const exists = await this.tableExists(schema.name);
      
      if (!exists) {
        console.log(`Creating table: ${schema.name}`);
        await query(schema.createSQL, []);
        console.log(`Table ${schema.name} created successfully`);
      }

      // Verify required columns if specified
      if (schema.requiredColumns && schema.requiredColumns.length > 0) {
        return await this.verifyTableColumns(schema.name, schema.requiredColumns);
      }

      return true;
    } catch (error) {
      console.error(`Error ensuring table ${schema.name}:`, error);
      return false;
    }
  }

  /**
   * Verify that a table has all required columns
   */
  public async verifyTableColumns(tableName: string, requiredColumns: string[]): Promise<boolean> {
    try {
      const result = await query(
        `SELECT column_name FROM information_schema.columns 
         WHERE table_schema = DATABASE() AND table_name = ?`,
        [tableName]
      ) as any[];

      if (!result || !Array.isArray(result)) {
        return false;
      }

      const existingColumns = result.map((row: any) => 
        (row.column_name || row.COLUMN_NAME || '').toLowerCase()
      );

      const missingColumns = requiredColumns.filter(col => 
        !existingColumns.includes(col.toLowerCase())
      );

      if (missingColumns.length > 0) {
        console.warn(`Table ${tableName} is missing columns: ${missingColumns.join(', ')}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Error verifying columns for table ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Get comprehensive database status
   */
  public async getDatabaseStatus(): Promise<DatabaseStatus> {
    const errors: string[] = [];
    
    // Check connection
    const connected = await this.ensureConnection();
    if (!connected) {
      errors.push('Database connection failed');
    }

    // Check critical tables
    const criticalTables = ['users', 'service_bookings', 'pets', 'notifications'];
    const tableStatus = await this.tablesExist(criticalTables);
    
    const missingTables = criticalTables.filter(table => !tableStatus[table]);
    const tablesExist = missingTables.length === 0;

    if (missingTables.length > 0) {
      errors.push(`Missing critical tables: ${missingTables.join(', ')}`);
    }

    return {
      connected,
      tablesExist,
      missingTables,
      errors
    };
  }

  /**
   * Ensure all critical tables exist
   */
  public async ensureCriticalTables(): Promise<boolean> {
    const tableSchemas: TableSchema[] = [
      {
        name: 'notifications',
        createSQL: `
          -- Table replaced with unified_notifications

    let allSuccessful = true;

    for (const schema of tableSchemas) {
      const success = await this.ensureTable(schema);
      if (!success) {
        allSuccessful = false;
        console.error(`Failed to ensure table: ${schema.name}`);
      }
    }

    return allSuccessful;
  }

  /**
   * Legacy compatibility methods
   */
  public async getDatabaseStatus().then(status => status.connected && status.tablesExist): Promise<boolean> {
    const status = await this.getDatabaseStatus();
    return status.connected && status.tablesExist;
  }

  public async ensureNotificationsTable(): Promise<boolean> {
    return await this.ensureTable({
      name: 'notifications',
      createSQL: `
        -- Table replaced with unified_notifications
  }

  public async ensureAdminNotificationsTable(): Promise<boolean> {
    return await this.ensureTable({
      name: 'admin_notifications',
      createSQL: `
        -- Table replaced with unified_notifications
  }

  /**
   * Clear health cache (useful for testing or after database changes)
   */
  public clearHealthCache(): void {
    this.healthCache = null;
  }
}

// Export singleton instance and convenience functions
export const databaseService = UnifiedDatabaseService.getInstance();

// Convenience functions for backward compatibility
export const ensureConnection = () => databaseService.ensureConnection();
export const getHealthStatus = () => databaseService.getHealthStatus();
export const tableExists = (tableName: string) => databaseService.tableExists(tableName);
export const tablesExist = (tableNames: string[]) => databaseService.tablesExist(tableNames);
export const ensureTable = (schema: TableSchema) => databaseService.ensureTable(schema);
export const getDatabaseStatus = () => databaseService.getDatabaseStatus();
export const ensureCriticalTables = () => databaseService.ensureCriticalTables();

// Legacy compatibility functions
export const checkDatabaseSetup = () => databaseService.getDatabaseStatus().then(status => status.connected && status.tablesExist);
export const ensureNotificationsTable = () => databaseService.ensureNotificationsTable();
export const ensureAdminNotificationsTable = () => databaseService.ensureAdminNotificationsTable();
