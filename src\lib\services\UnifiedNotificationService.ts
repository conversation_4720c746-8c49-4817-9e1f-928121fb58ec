/**
 * Unified Notification Service
 * Consolidates all notification functionality into a single, configurable service
 */

import { query } from '@/lib/db';
import { sendEmail } from '@/lib/consolidatedEmailService';
import { sendSMS, createBookingSMSMessage } from '@/lib/smsService';
import { createBookingConfirmationEmail, createBookingStatusUpdateEmail } from '@/lib/emailTemplates';

import { ensureConnection, getHealthStatus, tableExists, getDatabaseStatus, ensureNotificationsTable, ensureAdminNotificationsTable } from '@/lib/services/UnifiedDatabaseService';
// Types and Interfaces
export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type UserRole = 'user' | 'business' | 'admin' | 'system';
export type NotificationChannel = 'in_app' | 'email' | 'sms';

export interface NotificationConfig {
  type: UserRole;
  channels: NotificationChannel[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  template?: string;
  data?: Record<string, any>;
  shouldBroadcast?: boolean;
}

export interface NotificationRecipient {
  userId: number;
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  businessName?: string;
  emailNotificationsEnabled?: boolean;
  smsNotificationsEnabled?: boolean;
}

export interface NotificationResult {
  success: boolean;
  notificationId?: number;
  error?: string;
  channels?: {
    in_app?: boolean;
    email?: boolean;
    sms?: boolean;
  };
}

/**
 * Unified Notification Service Class
 */
export class UnifiedNotificationService {
  private static instance: UnifiedNotificationService;
  private broadcastFunction?: (userId: string, accountType: string, notification: any) => void;

  private constructor() {}

  public static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService();
    }
    return UnifiedNotificationService.instance;
  }

  /**
   * Set the broadcast function for real-time notifications
   */
  public setBroadcastFunction(broadcastFn: (userId: string, accountType: string, notification: any) => void) {
    this.broadcastFunction = broadcastFn;
  }

  /**
   * Send notification to a single user
   */
  public async sendToUser(
    userId: number,
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    try {
      const recipient = await this.getUserDetails(userId);
      if (!recipient) {
        return { success: false, error: 'User not found' };
      }

      return await this.sendNotification([recipient], title, message, notificationType, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send notification to multiple users
   */
  public async sendToUsers(
    userIds: number[],
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    try {
      const recipients = await this.getUsersDetails(userIds);
      return await this.sendNotification(recipients, title, message, notificationType, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send notification to all business users
   */
  public async sendToBusiness(
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    try {
      const businessUsers = await this.getBusinessUsers();
      return await this.sendNotification(businessUsers, title, message, notificationType, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send notification to all admin users
   */
  public async sendToAdmins(
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    try {
      const adminUsers = await this.getAdminUsers();
      return await this.sendNotification(adminUsers, title, message, notificationType, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send system-wide notification
   */
  public async sendSystemWide(
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    try {
      const allUsers = await this.getAllUsers();
      return await this.sendNotification(allUsers, title, message, notificationType, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send booking-specific notification
   */
  public async sendBookingNotification(
    bookingDetails: any,
    notificationType: string,
    additionalData?: any
  ): Promise<NotificationResult> {
    try {
      const { user_id, pet_name, service_name, booking_date, provider_name } = bookingDetails;
      
      let title: string;
      let message: string;
      let type: NotificationType;
      let link: string;

      // Generate notification content based on type
      switch (notificationType) {
        case 'booking_confirmed':
          title = 'Booking Confirmed';
          message = `Your booking for ${pet_name}'s ${service_name} has been confirmed for ${new Date(booking_date).toLocaleDateString()}.`;
          type = 'success';
          link = `/user/furparent_dashboard/bookings?bookingId=${bookingDetails.id}`;
          break;

        case 'booking_cancelled':
          title = 'Booking Cancelled';
          message = `Your booking for ${pet_name}'s ${service_name} has been cancelled.`;
          type = 'warning';
          link = `/user/furparent_dashboard/bookings`;
          break;

        case 'booking_completed':
          title = 'Service Completed';
          message = `The ${service_name} service for ${pet_name} has been completed by ${provider_name}.`;
          type = 'success';
          link = `/user/furparent_dashboard/bookings?bookingId=${bookingDetails.id}`;
          break;

        default:
          return { success: false, error: 'Invalid booking notification type' };
      }

      const config: NotificationConfig = {
        type: 'user',
        channels: ['in_app', 'email', 'sms'],
        priority: 'normal',
        shouldBroadcast: true
      };

      return await this.sendToUser(user_id, title, message, type, config, link);
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Core notification sending logic
   */
  private async sendNotification(
    recipients: NotificationRecipient[],
    title: string,
    message: string,
    notificationType: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<NotificationResult> {
    const results: NotificationResult = {
      success: false,
      channels: { in_app: false, email: false, sms: false }
    };

    let successCount = 0;
    let lastNotificationId: number | undefined;

    for (const recipient of recipients) {
      try {
        // Send in-app notification
        if (config.channels.includes('in_app')) {
          const inAppResult = await this.sendInAppNotification(
            recipient.userId,
            title,
            message,
            notificationType,
            link
          );
          
          if (inAppResult.success) {
            results.channels!.in_app = true;
            lastNotificationId = inAppResult.notificationId;
            successCount++;

            // Broadcast real-time notification
            if (config.shouldBroadcast && this.broadcastFunction) {
              this.broadcastFunction(recipient.userId.toString(), config.type, {
                id: lastNotificationId,
                title,
                message,
                type: notificationType,
                is_read: 0,
                link,
                created_at: new Date().toISOString()
              });
            }
          }
        }

        // Send email notification
        if (config.channels.includes('email') && recipient.email && recipient.emailNotificationsEnabled) {
          const emailResult = await this.sendEmailNotification(
            recipient,
            title,
            message,
            notificationType,
            config,
            link
          );
          
          if (emailResult) {
            results.channels!.email = true;
          }
        }

        // Send SMS notification
        if (config.channels.includes('sms') && recipient.phone && recipient.smsNotificationsEnabled) {
          const smsResult = await this.sendSMSNotification(
            recipient,
            title,
            message,
            notificationType
          );
          
          if (smsResult) {
            results.channels!.sms = true;
          }
        }

      } catch (error) {
        console.error(`Failed to send notification to user ${recipient.userId}:`, error);
      }
    }

    results.success = successCount > 0;
    results.notificationId = lastNotificationId;

    return results;
  }

  /**
   * Send in-app notification
   */
  private async sendInAppNotification(
    userId: number,
    title: string,
    message: string,
    type: NotificationType,
    link?: string
  ): Promise<NotificationResult> {
    try {
      await this.ensureNotificationsTable();

      const result = await query(
        `INSERT INTO `unified_notifications` (user_id, title, message, type, link, user_type) VALUES (?, ?, ?, ?, ?, "user")`,
        [userId, title, message, type, link]
      ) as any;

      return {
        success: true,
        notificationId: result.insertId
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(
    recipient: NotificationRecipient,
    title: string,
    message: string,
    type: NotificationType,
    config: NotificationConfig,
    link?: string
  ): Promise<boolean> {
    try {
      const emailHtml = this.createEmailTemplate(recipient, title, message, type, config, link);
      const emailText = this.createEmailText(recipient, title, message, link);

      await sendEmail({
        to: recipient.email!,
        subject: `[Rainbow Paws] ${title}`,
        html: emailHtml,
        text: emailText
      });

      return true;
    } catch (error) {
      console.error('Email notification failed:', error);
      return false;
    }
  }

  /**
   * Send SMS notification
   */
  private async sendSMSNotification(
    recipient: NotificationRecipient,
    title: string,
    message: string,
    type: NotificationType
  ): Promise<boolean> {
    try {
      const smsMessage = `${title}: ${message}`;
      
      await sendSMS({
        to: recipient.phone!,
        message: smsMessage
      });

      return true;
    } catch (error) {
      console.error('SMS notification failed:', error);
      return false;
    }
  }

  // Helper methods for database operations
  private async getUserDetails(userId: number): Promise<NotificationRecipient | null> {
    try {
      const result = await query(
        `SELECT user_id, email, phone, first_name, last_name, email_notifications, sms_notifications
         FROM users WHERE user_id = ?`,
        [userId]
      ) as any[];

      if (result.length === 0) return null;

      const user = result[0];
      return {
        userId: user.user_id,
        email: user.email,
        phone: user.phone,
        firstName: user.first_name,
        lastName: user.last_name,
        emailNotificationsEnabled: user.email_notifications !== 0,
        smsNotificationsEnabled: user.sms_notifications !== 0
      };
    } catch (error) {
      console.error('Error getting user details:', error);
      return null;
    }
  }

  private async getUsersDetails(userIds: number[]): Promise<NotificationRecipient[]> {
    try {
      const placeholders = userIds.map(() => '?').join(',');
      const result = await query(
        `SELECT user_id, email, phone, first_name, last_name, email_notifications, sms_notifications
         FROM users WHERE user_id IN (${placeholders})`,
        userIds
      ) as any[];

      return result.map(user => ({
        userId: user.user_id,
        email: user.email,
        phone: user.phone,
        firstName: user.first_name,
        lastName: user.last_name,
        emailNotificationsEnabled: user.email_notifications !== 0,
        smsNotificationsEnabled: user.sms_notifications !== 0
      }));
    } catch (error) {
      console.error('Error getting users details:', error);
      return [];
    }
  }

  private async getBusinessUsers(): Promise<NotificationRecipient[]> {
    try {
      const result = await query(
        `SELECT u.user_id, u.email, u.phone, u.first_name, u.last_name, u.email_notifications, u.sms_notifications, sp.name as business_name
         FROM users u
         LEFT JOIN service_providers sp ON sp.user_id = u.user_id
         WHERE u.role = 'business'`,
        []
      ) as any[];

      return result.map(user => ({
        userId: user.user_id,
        email: user.email,
        phone: user.phone,
        firstName: user.first_name,
        lastName: user.last_name,
        businessName: user.business_name,
        emailNotificationsEnabled: user.email_notifications !== 0,
        smsNotificationsEnabled: user.sms_notifications !== 0
      }));
    } catch (error) {
      console.error('Error getting business users:', error);
      return [];
    }
  }

  private async getAdminUsers(): Promise<NotificationRecipient[]> {
    try {
      const result = await query(
        `SELECT user_id, email, phone, first_name, last_name, email_notifications, sms_notifications
         FROM users WHERE role = 'admin'`,
        []
      ) as any[];

      return result.map(user => ({
        userId: user.user_id,
        email: user.email,
        phone: user.phone,
        firstName: user.first_name,
        lastName: user.last_name,
        emailNotificationsEnabled: user.email_notifications !== 0,
        smsNotificationsEnabled: user.sms_notifications !== 0
      }));
    } catch (error) {
      console.error('Error getting admin users:', error);
      return [];
    }
  }

  private async getAllUsers(): Promise<NotificationRecipient[]> {
    try {
      const result = await query(
        `SELECT user_id, email, phone, first_name, last_name, email_notifications, sms_notifications
         FROM users`,
        []
      ) as any[];

      return result.map(user => ({
        userId: user.user_id,
        email: user.email,
        phone: user.phone,
        firstName: user.first_name,
        lastName: user.last_name,
        emailNotificationsEnabled: user.email_notifications !== 0,
        smsNotificationsEnabled: user.sms_notifications !== 0
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  private async ensureNotificationsTable(): Promise<void> {
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS unified_notifications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NULL,
          user_type ENUM('user', 'admin', 'business') NOT NULL DEFAULT 'user',
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          type ENUM('info', 'success', 'warning', 'error', 'system', 'booking', 'payment', 'restriction', 'appeal') DEFAULT 'info',
          entity_type VARCHAR(100) NULL,
          entity_id INT NULL,
          link VARCHAR(255) NULL,
          is_read TINYINT(1) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_user_type (user_type),
          INDEX idx_type (type),
          INDEX idx_created_at (created_at),
          INDEX idx_is_read (is_read),
          INDEX idx_entity (entity_type, entity_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `, []);
    } catch (error) {
      console.error('Error ensuring unified_notifications table:', error);
    }
  }

  private createEmailTemplate(
    recipient: NotificationRecipient,
    title: string,
    message: string,
    type: NotificationType,
    config: NotificationConfig,
    link?: string
  ): string {
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const name = recipient.firstName || 'User';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2c3e50;">${title}</h2>
          <p>Hello ${name},</p>
          <p>${message}</p>
          ${link ? `<p><a href="${appUrl}${link}" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Details</a></p>` : ''}
          <hr style="margin: 20px 0;">
          <p style="font-size: 12px; color: #666;">
            This is an automated message from Rainbow Paws. Please do not reply to this email.
          </p>
        </div>
      </body>
      </html>
    `;
  }

  private createEmailText(
    recipient: NotificationRecipient,
    title: string,
    message: string,
    link?: string
  ): string {
    const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const name = recipient.firstName || 'User';
    
    return `
      ${title}
      
      Hello ${name},
      
      ${message}
      
      ${link ? `View details: ${appUrl}${link}` : ''}
      
      ---
      This is an automated message from Rainbow Paws.
    `;
  }
}

// Export singleton instance
export const notificationService = UnifiedNotificationService.getInstance();
