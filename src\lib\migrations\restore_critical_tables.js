#!/usr/bin/env node

/**
 * Restore critical tables that were accidentally removed
 */

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../../.env.local') });

class CriticalTablesRestorer {
  constructor() {
    this.connection = null;
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'rainbow_paws',
      port: parseInt(process.env.DB_PORT) || 3306
    };
    this.restored = [];
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ Connected to database');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async restoreCriticalTables() {
    console.log('🔧 Restoring critical tables that were accidentally removed...\n');

    // Restore OTP tables
    await this.restoreOtpTables();

    // Restore businesses table
    await this.restoreBusinessesTable();

    // Restore other missing critical tables
    await this.restoreOtherTables();

    console.log(`\n✅ Restoration completed. ${this.restored.length} tables restored.`);
    
    if (this.restored.length > 0) {
      console.log('\n📋 Tables restored:');
      this.restored.forEach((table, index) => {
        console.log(`${index + 1}. ${table}`);
      });
    }

    await this.connection.end();
  }

  async restoreOtpTables() {
    console.log('🔐 RESTORING OTP TABLES\n');
    console.log('=' .repeat(25));

    // Restore otp_codes table
    await this.createTableIfNotExists('otp_codes', `
      CREATE TABLE otp_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('user', 'business', 'admin') NOT NULL DEFAULT 'user',
        otp_code VARCHAR(6) NOT NULL,
        purpose ENUM('registration', 'login', 'password_reset', 'phone_verification') NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        used TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_otp_code (otp_code),
        INDEX idx_expires_at (expires_at),
        INDEX idx_user_type (user_type),
        INDEX idx_purpose (purpose)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    // Restore otp_attempts table
    await this.createTableIfNotExists('otp_attempts', `
      CREATE TABLE otp_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL,
        attempt_type ENUM('otp_generation', 'otp_verification') NOT NULL,
        attempts INT DEFAULT 1,
        last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        blocked_until TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_identifier (identifier),
        INDEX idx_attempt_type (attempt_type),
        INDEX idx_blocked_until (blocked_until)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    console.log('✅ OTP tables restored\n');
  }

  async restoreBusinessesTable() {
    console.log('🏢 RESTORING BUSINESSES TABLE\n');
    console.log('=' .repeat(30));

    await this.createTableIfNotExists('businesses', `
      CREATE TABLE businesses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        business_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20) NOT NULL,
        address TEXT NOT NULL,
        city VARCHAR(100) NOT NULL,
        province VARCHAR(100) NOT NULL,
        postal_code VARCHAR(20) NOT NULL,
        business_type ENUM('cremation', 'veterinary', 'pet_services') NOT NULL DEFAULT 'cremation',
        license_number VARCHAR(100) NULL,
        description TEXT NULL,
        website VARCHAR(255) NULL,
        profile_picture VARCHAR(255) NULL,
        business_hours JSON NULL,
        services_offered JSON NULL,
        pricing_info JSON NULL,
        status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
        verification_documents JSON NULL,
        password_hash VARCHAR(255) NOT NULL,
        email_verified TINYINT(1) DEFAULT 0,
        phone_verified TINYINT(1) DEFAULT 0,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_phone (phone),
        INDEX idx_business_type (business_type),
        INDEX idx_status (status),
        INDEX idx_city (city),
        INDEX idx_province (province)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    console.log('✅ Businesses table restored\n');
  }

  async restoreOtherTables() {
    console.log('📋 RESTORING OTHER CRITICAL TABLES\n');
    console.log('=' .repeat(35));

    // Restore reviews table
    await this.createTableIfNotExists('reviews', `
      CREATE TABLE reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        business_id INT NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review_text TEXT NULL,
        response_text TEXT NULL,
        response_date TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_booking_id (booking_id),
        INDEX idx_user_id (user_id),
        INDEX idx_business_id (business_id),
        INDEX idx_rating (rating),
        UNIQUE KEY unique_booking_review (booking_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    // Restore refunds table
    await this.createTableIfNotExists('refunds', `
      CREATE TABLE refunds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        business_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        reason TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected', 'processed') DEFAULT 'pending',
        admin_notes TEXT NULL,
        processed_by INT NULL,
        processed_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_booking_id (booking_id),
        INDEX idx_user_id (user_id),
        INDEX idx_business_id (business_id),
        INDEX idx_status (status),
        INDEX idx_processed_by (processed_by)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    // Restore user_appeals table
    await this.createTableIfNotExists('user_appeals', `
      CREATE TABLE user_appeals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('user', 'business') NOT NULL,
        restriction_id INT NULL,
        appeal_reason TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        admin_response TEXT NULL,
        reviewed_by INT NULL,
        reviewed_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_user_type (user_type),
        INDEX idx_status (status),
        INDEX idx_reviewed_by (reviewed_by)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    // Restore service_packages table
    await this.createTableIfNotExists('service_packages', `
      CREATE TABLE service_packages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        business_id INT NOT NULL,
        package_name VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        duration_hours INT NULL,
        inclusions JSON NULL,
        package_type ENUM('cremation', 'memorial', 'full_service') NOT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_business_id (business_id),
        INDEX idx_package_type (package_type),
        INDEX idx_is_active (is_active),
        INDEX idx_price (price)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);

    console.log('✅ Other critical tables restored\n');
  }

  async createTableIfNotExists(tableName, createSQL) {
    try {
      // Check if table exists
      const [exists] = await this.connection.execute(`
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = ? AND table_name = ?
      `, [this.dbConfig.database, tableName]);

      if (exists[0].count === 0) {
        console.log(`📋 Creating table: ${tableName}`);
        await this.connection.execute(createSQL);
        this.restored.push(tableName);
        console.log(`✅ Table ${tableName} created successfully`);
      } else {
        console.log(`ℹ️  Table ${tableName} already exists, skipping`);
      }
    } catch (error) {
      console.error(`❌ Error creating table ${tableName}:`, error.message);
    }
  }
}

// Run the restoration
if (require.main === module) {
  const restorer = new CriticalTablesRestorer();
  restorer.connect()
    .then(() => restorer.restoreCriticalTables())
    .then(() => {
      console.log('\n🎉 Critical tables restoration completed successfully!');
      console.log('\n📝 Next steps:');
      console.log('1. Verify all tables are working correctly');
      console.log('2. Test OTP functionality');
      console.log('3. Test business registration and management');
      console.log('4. Run application build and tests');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Restoration failed:', error);
      process.exit(1);
    });
}

module.exports = CriticalTablesRestorer;
