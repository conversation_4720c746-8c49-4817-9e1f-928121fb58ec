/**
 * Unified Validation Service
 * Consolidates all validation logic into a single, reusable service
 */

// Types and Interfaces
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  errors?: Record<string, string>;
  sanitized?: any;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'phone' | 'url' | 'date' | 'boolean';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => ValidationResult;
  sanitize?: boolean;
}

export interface PhoneValidationOptions {
  country?: 'PH' | 'US' | 'international';
  format?: 'international' | 'national' | 'e164';
}

export interface EmailValidationOptions {
  allowInternational?: boolean;
  checkMX?: boolean;
}

/**
 * Unified Validation Service Class
 */
export class UnifiedValidationService {
  private static instance: UnifiedValidationService;

  // Common validation patterns
  private readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private readonly PHONE_REGEX = /^\+?[\d\s\-\(\)]+$/;
  private readonly URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

  private constructor() {}

  public static getInstance(): UnifiedValidationService {
    if (!UnifiedValidationService.instance) {
      UnifiedValidationService.instance = new UnifiedValidationService();
    }
    return UnifiedValidationService.instance;
  }

  /**
   * Validate email address
   */
  public validateEmail(email: string, options: EmailValidationOptions = {}): ValidationResult {
    if (!email || typeof email !== 'string') {
      return { isValid: false, error: 'Email is required' };
    }

    const trimmedEmail = email.trim();
    
    if (!trimmedEmail) {
      return { isValid: false, error: 'Email is required' };
    }

    // Basic format validation
    if (!this.EMAIL_REGEX.test(trimmedEmail)) {
      return { isValid: false, error: 'Invalid email format' };
    }

    // Additional validations
    if (trimmedEmail.length > 254) {
      return { isValid: false, error: 'Email address is too long' };
    }

    const [localPart, domain] = trimmedEmail.split('@');
    
    if (localPart.length > 64) {
      return { isValid: false, error: 'Email local part is too long' };
    }

    if (domain.length > 253) {
      return { isValid: false, error: 'Email domain is too long' };
    }

    return { isValid: true, sanitized: trimmedEmail.toLowerCase() };
  }

  /**
   * Validate and format Philippine phone number
   */
  public validatePhone(phone: string, options: PhoneValidationOptions = {}): ValidationResult {
    if (!phone || typeof phone !== 'string') {
      return { isValid: false, error: 'Phone number is required' };
    }

    const trimmedPhone = phone.trim();
    
    if (!trimmedPhone) {
      return { isValid: false, error: 'Phone number is required' };
    }

    // Basic format check
    if (!this.PHONE_REGEX.test(trimmedPhone)) {
      return { isValid: false, error: 'Invalid phone number format' };
    }

    // Philippine phone number validation
    if (options.country === 'PH' || !options.country) {
      return this.validatePhilippinePhone(trimmedPhone, options.format);
    }

    // International phone validation (basic)
    const cleanNumber = trimmedPhone.replace(/\D/g, '');
    if (cleanNumber.length < 7 || cleanNumber.length > 15) {
      return { isValid: false, error: 'Invalid phone number length' };
    }

    return { isValid: true, sanitized: trimmedPhone };
  }

  /**
   * Validate input against a schema
   */
  public validateInput(data: any, schema: ValidationSchema): ValidationResult {
    const errors: Record<string, string> = {};
    const sanitized: any = {};

    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field];
      const fieldResult = this.validateField(value, rule, field);

      if (!fieldResult.isValid) {
        errors[field] = fieldResult.error || `Invalid ${field}`;
      } else if (fieldResult.sanitized !== undefined) {
        sanitized[field] = fieldResult.sanitized;
      } else {
        sanitized[field] = value;
      }
    }

    const isValid = Object.keys(errors).length === 0;
    
    return {
      isValid,
      errors: isValid ? undefined : errors,
      sanitized: isValid ? sanitized : undefined
    };
  }

  /**
   * Sanitize input data
   */
  public sanitizeInput(data: any): any {
    if (typeof data === 'string') {
      return this.sanitizeString(data);
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeInput(item));
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }

    return data;
  }

  /**
   * Validate required fields
   */
  public validateRequiredFields(data: any, requiredFields: string[]): ValidationResult {
    const missingFields = requiredFields.filter(field => {
      const value = data[field];
      return value === undefined || value === null || 
             (typeof value === 'string' && !value.trim());
    });

    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: 'Missing required fields',
        errors: missingFields.reduce((acc, field) => {
          acc[field] = `${field} is required`;
          return acc;
        }, {} as Record<string, string>)
      };
    }

    return { isValid: true };
  }

  /**
   * Validate form data with common patterns
   */
  public validateForm(data: any, validationRules: {
    required?: string[];
    email?: string[];
    phone?: string[];
    minLength?: Record<string, number>;
    maxLength?: Record<string, number>;
    custom?: Record<string, (value: any) => ValidationResult>;
  }): ValidationResult {
    const errors: Record<string, string> = {};

    // Check required fields
    if (validationRules.required) {
      const requiredResult = this.validateRequiredFields(data, validationRules.required);
      if (!requiredResult.isValid && requiredResult.errors) {
        Object.assign(errors, requiredResult.errors);
      }
    }

    // Validate email fields
    if (validationRules.email) {
      for (const field of validationRules.email) {
        if (data[field]) {
          const emailResult = this.validateEmail(data[field]);
          if (!emailResult.isValid) {
            errors[field] = emailResult.error || 'Invalid email format';
          }
        }
      }
    }

    // Validate phone fields
    if (validationRules.phone) {
      for (const field of validationRules.phone) {
        if (data[field]) {
          const phoneResult = this.validatePhone(data[field], { country: 'PH' });
          if (!phoneResult.isValid) {
            errors[field] = phoneResult.error || 'Invalid phone number';
          }
        }
      }
    }

    // Validate minimum length
    if (validationRules.minLength) {
      for (const [field, minLen] of Object.entries(validationRules.minLength)) {
        const value = data[field];
        if (value && typeof value === 'string' && value.trim().length < minLen) {
          errors[field] = `${field} must be at least ${minLen} characters`;
        }
      }
    }

    // Validate maximum length
    if (validationRules.maxLength) {
      for (const [field, maxLen] of Object.entries(validationRules.maxLength)) {
        const value = data[field];
        if (value && typeof value === 'string' && value.trim().length > maxLen) {
          errors[field] = `${field} must be no more than ${maxLen} characters`;
        }
      }
    }

    // Custom validations
    if (validationRules.custom) {
      for (const [field, validator] of Object.entries(validationRules.custom)) {
        if (data[field] !== undefined) {
          const customResult = validator(data[field]);
          if (!customResult.isValid) {
            errors[field] = customResult.error || `Invalid ${field}`;
          }
        }
      }
    }

    const isValid = Object.keys(errors).length === 0;
    
    return {
      isValid,
      errors: isValid ? undefined : errors
    };
  }

  // Private helper methods
  private validateField(value: any, rule: ValidationRule, fieldName: string): ValidationResult {
    // Check required
    if (rule.required && (value === undefined || value === null || 
        (typeof value === 'string' && !value.trim()))) {
      return { isValid: false, error: `${fieldName} is required` };
    }

    // If value is empty and not required, it's valid
    if (!rule.required && (value === undefined || value === null || 
        (typeof value === 'string' && !value.trim()))) {
      return { isValid: true };
    }

    // Type validation
    if (rule.type) {
      const typeResult = this.validateType(value, rule.type);
      if (!typeResult.isValid) {
        return typeResult;
      }
      value = typeResult.sanitized || value;
    }

    // Length validations for strings
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return { isValid: false, error: `${fieldName} must be at least ${rule.minLength} characters` };
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        return { isValid: false, error: `${fieldName} must be no more than ${rule.maxLength} characters` };
      }
    }

    // Numeric validations
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        return { isValid: false, error: `${fieldName} must be at least ${rule.min}` };
      }
      if (rule.max !== undefined && value > rule.max) {
        return { isValid: false, error: `${fieldName} must be no more than ${rule.max}` };
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return { isValid: false, error: `${fieldName} format is invalid` };
    }

    // Custom validation
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (!customResult.isValid) {
        return customResult;
      }
    }

    // Sanitization
    let sanitized = value;
    if (rule.sanitize && typeof value === 'string') {
      sanitized = this.sanitizeString(value);
    }

    return { isValid: true, sanitized };
  }

  private validateType(value: any, type: string): ValidationResult {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          return { isValid: false, error: 'Must be a string' };
        }
        return { isValid: true, sanitized: value.trim() };

      case 'number':
        const num = Number(value);
        if (isNaN(num)) {
          return { isValid: false, error: 'Must be a number' };
        }
        return { isValid: true, sanitized: num };

      case 'email':
        return this.validateEmail(value);

      case 'phone':
        return this.validatePhone(value, { country: 'PH' });

      case 'url':
        if (typeof value !== 'string' || !this.URL_REGEX.test(value)) {
          return { isValid: false, error: 'Must be a valid URL' };
        }
        return { isValid: true };

      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return { isValid: false, error: 'Must be a valid date' };
        }
        return { isValid: true, sanitized: date };

      case 'boolean':
        if (typeof value === 'boolean') {
          return { isValid: true };
        }
        if (value === 'true' || value === '1' || value === 1) {
          return { isValid: true, sanitized: true };
        }
        if (value === 'false' || value === '0' || value === 0) {
          return { isValid: true, sanitized: false };
        }
        return { isValid: false, error: 'Must be a boolean' };

      default:
        return { isValid: true };
    }
  }

  private validatePhilippinePhone(phone: string, format?: string): ValidationResult {
    try {
      // Remove all non-digit characters
      let cleanNumber = phone.replace(/\D/g, '');

      // Handle different input formats
      if (cleanNumber.startsWith('63')) {
        cleanNumber = cleanNumber.substring(2);
      } else if (cleanNumber.startsWith('0')) {
        cleanNumber = cleanNumber.substring(1);
      }

      // Validate length
      if (cleanNumber.length !== 10) {
        return {
          isValid: false,
          error: `Invalid Philippine phone number: expected 10 digits, got ${cleanNumber.length}`
        };
      }

      // Validate that it starts with 9 (Philippine mobile numbers)
      if (!cleanNumber.startsWith('9')) {
        return {
          isValid: false,
          error: 'Invalid Philippine mobile number: must start with 9'
        };
      }

      // Format based on requested format
      let formatted: string;
      switch (format) {
        case 'national':
          formatted = `0${cleanNumber}`;
          break;
        case 'e164':
        case 'international':
        default:
          formatted = `+63${cleanNumber}`;
          break;
      }

      return { isValid: true, sanitized: formatted };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Invalid phone number'
      };
    }
  }

  private sanitizeString(str: string): string {
    return str
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes that could cause issues
      .replace(/\s+/g, ' '); // Normalize whitespace
  }
}

// Export singleton instance and convenience functions
export const validationService = UnifiedValidationService.getInstance();

// Convenience functions for backward compatibility
export const validateEmail = (email: string, options?: EmailValidationOptions) => 
  validationService.validateEmail(email, options);

export const validatePhone = (phone: string, options?: PhoneValidationOptions) => 
  validationService.validatePhone(phone, options);

export const validateInput = (data: any, schema: ValidationSchema) => 
  validationService.validateInput(data, schema);

export const sanitizeInput = (data: any) => 
  validationService.sanitizeInput(data);

export const validateRequiredFields = (data: any, requiredFields: string[]) => 
  validationService.validateRequiredFields(data, requiredFields);

export const validateForm = (data: any, rules: any) => 
  validationService.validateForm(data, rules);

// Legacy compatibility functions
export const formatPhoneNumber = (phoneNumber: string | undefined): string | null => {
  if (!phoneNumber) return null;
  
  const result = validationService.validatePhone(phoneNumber, { country: 'PH', format: 'international' });
  return result.isValid ? result.sanitized || null : null;
};

export const testPhoneNumberFormatting = (phoneNumber: string): {
  success: boolean;
  formatted?: string;
  error?: string;
} => {
  const result = validationService.validatePhone(phoneNumber, { country: 'PH', format: 'international' });
  return {
    success: result.isValid,
    formatted: result.sanitized,
    error: result.error
  };
};
