-- Missing otp_codes table
-- CREATE TABLE otp_codes for OTP verification
CREATE TABLE otp_codes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          user_type <PERSON><PERSON><PERSON>('user', 'business', 'admin') NOT NULL DEFAULT 'user',
          otp_code VARCHAR(6) NOT NULL,
          purpose ENUM('registration', 'login', 'password_reset', 'phone_verification') NOT NULL,
          expires_at TIMESTAMP NOT NULL,
          used TINYINT(1) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_otp_code (otp_code),
          INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Missing otp_attempts table
-- CREATE TABLE otp_attempts for rate limiting
CREATE TABLE otp_attempts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          identifier VARCHAR(255) NOT NULL,
          attempt_type <PERSON>NUM('otp_generation', 'otp_verification') NOT NULL,
          attempts INT DEFAULT 1,
          last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          blocked_until TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_identifier (identifier),
          INDEX idx_blocked_until (blocked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
