/**
 * Unified Image Service
 * Consolidates all image handling functionality into a single, comprehensive service
 */

import { join } from 'path';
import fs from 'fs';
import { writeFile, mkdir } from 'fs/promises';

// Types and Interfaces
export interface ImageOptions {
  addCacheBust?: boolean;
  fallback?: string;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
}

export interface UploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  directory?: string;
  filename?: string;
  preserveOriginalName?: boolean;
}

export interface UploadResult {
  success: boolean;
  imagePath?: string;
  imageUrl?: string;
  error?: string;
  filename?: string;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  fileSize?: number;
  fileType?: string;
}

export type ImageType = 'profile' | 'pet' | 'package' | 'document' | 'business';

/**
 * Unified Image Service Class
 */
export class UnifiedImageService {
  private static instance: UnifiedImageService;
  
  // Default configuration
  private readonly DEFAULT_FALLBACK = '/bg_4.png';
  private readonly DEFAULT_MAX_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  private readonly UPLOAD_BASE_DIR = 'public/uploads';

  private constructor() {}

  public static getInstance(): UnifiedImageService {
    if (!UnifiedImageService.instance) {
      UnifiedImageService.instance = new UnifiedImageService();
    }
    return UnifiedImageService.instance;
  }

  /**
   * Get optimized image path with proper API routing
   */
  public getPath(imagePath: string, options: ImageOptions = {}): string {
    if (!imagePath) {
      return options.fallback || this.DEFAULT_FALLBACK;
    }

    // If it's already an API path, return as is (unless cache busting is requested)
    if (imagePath.startsWith('/api/image/')) {
      return options.addCacheBust ? this.addCacheBuster(imagePath) : imagePath;
    }

    // If it's an uploads path, convert to API path
    if (imagePath.startsWith('/uploads/')) {
      const uploadPath = imagePath.substring('/uploads/'.length);
      const apiPath = `/api/image/${uploadPath}`;
      return options.addCacheBust ? this.addCacheBuster(apiPath) : apiPath;
    }

    // Handle specific image types
    const apiPath = this.convertToApiPath(imagePath);
    return options.addCacheBust ? this.addCacheBuster(apiPath) : apiPath;
  }

  /**
   * Get production-ready image path with cache busting
   */
  public getProductionPath(imagePath: string, options: ImageOptions = {}): string {
    return this.getPath(imagePath, { ...options, addCacheBust: true });
  }

  /**
   * Get profile picture URL without cache busting to prevent flickering
   */
  public getProfilePictureUrl(profilePicturePath: string | null | undefined, options: ImageOptions = {}): string {
    if (!profilePicturePath) {
      return options.fallback || this.DEFAULT_FALLBACK;
    }

    // Don't add cache busting for profile pictures to prevent flickering during navigation
    return this.getPath(profilePicturePath, { ...options, addCacheBust: false });
  }

  /**
   * Get all available images for a package
   */
  public async getPackageImages(packageId: number | string): Promise<string[]> {
    try {
      // Try to fetch available images from our API
      const response = await fetch(`/api/packages/available-images?id=${packageId}`);
      const data = await response.json();

      // If we found images, return them all
      if (data.success && data.imagesFound && data.imagesFound.length > 0) {
        return data.imagesFound.map((img: string) => this.getPath(img));
      }

      // Fallback to default image
      return [this.DEFAULT_FALLBACK];
    } catch (error) {
      console.error('Error fetching package images:', error);
      return [this.DEFAULT_FALLBACK];
    }
  }

  /**
   * Validate image file
   */
  public validateImage(file: File, options: UploadOptions = {}): ValidationResult {
    const maxSize = options.maxSize || this.DEFAULT_MAX_SIZE;
    const allowedTypes = options.allowedTypes || this.ALLOWED_TYPES;

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`,
        fileType: file.type
      };
    }

    // Check file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return {
        isValid: false,
        error: `File size exceeds the limit (${maxSizeMB}MB)`,
        fileSize: file.size
      };
    }

    return {
      isValid: true,
      fileSize: file.size,
      fileType: file.type
    };
  }

  /**
   * Process image upload
   */
  public async processUpload(
    file: File, 
    type: ImageType, 
    userId: string, 
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate the file
      const validation = this.validateImage(file, options);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Generate filename and directory
      const { filename, directory, relativePath } = this.generateUploadPaths(file, type, userId, options);

      // Ensure directory exists
      const fullDirectory = join(process.cwd(), this.UPLOAD_BASE_DIR, directory);
      await this.ensureDirectoryExists(fullDirectory);

      // Save the file
      const fullPath = join(fullDirectory, filename);
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(fullPath, buffer);

      return {
        success: true,
        imagePath: relativePath,
        imageUrl: relativePath, // For backward compatibility
        filename
      };

    } catch (error) {
      console.error('Error processing image upload:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Handle image loading errors
   */
  public handleImageError(event: React.SyntheticEvent<HTMLImageElement>, fallback?: string): void {
    const target = event.target as HTMLImageElement;
    target.src = fallback || this.DEFAULT_FALLBACK;
    target.classList.remove('error');
    target.classList.add('fallback-image');
  }

  /**
   * Upload profile picture with AJAX
   */
  public async uploadProfilePicture(
    file: File,
    apiEndpoint: string,
    userType: 'user' | 'admin' | 'business' = 'user',
    additionalData?: Record<string, string>
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateImage(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Create form data
      const formData = new FormData();
      formData.append('profilePicture', file);
      formData.append('userType', userType);

      // Add additional data if provided
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      // Upload the file
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error || 'Upload failed'
        };
      }

      return {
        success: true,
        imagePath: result.profilePicturePath,
        imageUrl: result.profilePicturePath
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Get content type for file extension
   */
  public getContentType(extension: string): string {
    const contentTypes: { [key: string]: string } = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      'bmp': 'image/bmp',
      'ico': 'image/x-icon'
    };

    return contentTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Find image file in multiple possible locations
   */
  public findImageFile(imagePath: string): string | null {
    const possiblePaths = [
      join(process.cwd(), 'public', 'uploads', imagePath),
      join(process.cwd(), 'uploads', imagePath),
      join(process.cwd(), '.next', 'server', 'public', 'uploads', imagePath),
      join(process.cwd(), '.next', 'standalone', 'public', 'uploads', imagePath),
      join(process.cwd(), 'public', imagePath),
      join(process.cwd(), 'public', 'documents', imagePath.split('/').pop() || ''),
      join(process.cwd(), 'documents', imagePath.split('/').pop() || '')
    ];

    for (const path of possiblePaths) {
      if (fs.existsSync(path)) {
        return path;
      }
    }

    return null;
  }

  // Private helper methods
  private addCacheBuster(url: string): string {
    const timestamp = Date.now();
    return `${url}${url.includes('?') ? '&' : '?'}t=${timestamp}`;
  }

  private convertToApiPath(imagePath: string): string {
    // Handle package images
    if (imagePath.includes('packages/') && !imagePath.startsWith('/api/')) {
      const parts = imagePath.split('packages/');
      if (parts.length > 1) {
        return `/api/image/packages/${parts[1]}`;
      }
    }

    // Handle pet images
    if (imagePath.includes('pets/') && !imagePath.startsWith('/api/')) {
      const parts = imagePath.split('pets/');
      if (parts.length > 1) {
        return `/api/image/pets/${parts[1]}`;
      }
    }

    // Handle document paths
    if ((imagePath.includes('documents/') || imagePath.includes('business/') || imagePath.includes('businesses/'))
        && !imagePath.startsWith('/api/')) {
      const parts = imagePath.split('/');
      const relevantIndex = parts.findIndex(part =>
        part === 'documents' || part === 'business' || part === 'businesses'
      );

      if (relevantIndex >= 0) {
        const relevantPath = parts.slice(relevantIndex).join('/');
        return `/api/image/${relevantPath}`;
      }
    }

    // For other paths, return as is
    return imagePath;
  }

  private generateUploadPaths(
    file: File, 
    type: ImageType, 
    userId: string, 
    options: UploadOptions
  ): { filename: string; directory: string; relativePath: string } {
    const timestamp = Date.now();
    const originalName = file.name.replace(/\s+/g, '_').toLowerCase();
    const extension = originalName.split('.').pop() || 'jpg';

    let directory: string;
    let filename: string;

    if (options.filename) {
      filename = options.filename;
    } else if (options.preserveOriginalName) {
      filename = `${timestamp}_${originalName}`;
    } else {
      // Generate filename based on type
      switch (type) {
        case 'profile':
          filename = `profile_${userId}_${timestamp}.${extension}`;
          break;
        case 'pet':
          filename = `pet_${userId}_${timestamp}.${extension}`;
          break;
        case 'package':
          filename = `package_${userId}_${timestamp}.${extension}`;
          break;
        case 'document':
          filename = `document_${userId}_${timestamp}.${extension}`;
          break;
        case 'business':
          filename = `business_${userId}_${timestamp}.${extension}`;
          break;
        default:
          filename = `${type}_${userId}_${timestamp}.${extension}`;
      }
    }

    // Determine directory based on type
    if (options.directory) {
      directory = options.directory;
    } else {
      switch (type) {
        case 'profile':
          directory = 'profile-pictures';
          break;
        case 'pet':
          directory = 'pets';
          break;
        case 'package':
          directory = 'packages';
          break;
        case 'document':
          directory = 'documents';
          break;
        case 'business':
          directory = 'businesses';
          break;
        default:
          directory = type;
      }
    }

    const relativePath = `/uploads/${directory}/${filename}`;

    return { filename, directory, relativePath };
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await mkdir(dirPath, { recursive: true });
    } catch (error) {
      if ((error as any).code !== 'EEXIST') {
        throw error;
      }
    }
  }
}

// Export singleton instance and convenience functions
export const imageService = UnifiedImageService.getInstance();

// Convenience functions for backward compatibility
export const getImagePath = (path: string, addCacheBust?: boolean) => 
  imageService.getPath(path, { addCacheBust });

export const getProductionImagePath = (path: string) => 
  imageService.getProductionPath(path);

export const getProfilePictureUrl = (profilePicturePath: string | null | undefined, userId?: string | number) => 
  imageService.getProfilePictureUrl(profilePicturePath);

export const getAllPackageImages = (packageId: number | string) => 
  imageService.getPackageImages(packageId);

export const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>, fallback?: string) => 
  imageService.handleImageError(event, fallback);

export const uploadProfilePictureAjax = (
  file: File,
  apiEndpoint: string,
  userType: 'user' | 'admin' | 'business' = 'user',
  additionalData?: Record<string, string>
) => imageService.uploadProfilePicture(file, apiEndpoint, userType, additionalData);

export const addCacheBuster = (url: string) => 
  imageService.getProductionPath(url, { addCacheBust: true });

// New unified functions
export const validateImage = (file: File, options?: UploadOptions) => 
  imageService.validateImage(file, options);

export const processUpload = (file: File, type: ImageType, userId: string, options?: UploadOptions) => 
  imageService.processUpload(file, type, userId, options);

export const getContentType = (extension: string) => 
  imageService.getContentType(extension);

export const findImageFile = (imagePath: string) => 
  imageService.findImageFile(imagePath);
