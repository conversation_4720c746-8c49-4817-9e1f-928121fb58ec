#!/usr/bin/env node

/**
 * Check current database state and identify missing critical tables
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../../.env.local') });

class DatabaseStateChecker {
  constructor() {
    this.connection = null;
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'rainbow_paws',
      port: parseInt(process.env.DB_PORT) || 3306
    };
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ Connected to database');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async checkDatabaseState() {
    console.log('🔍 Checking current database state...\n');

    // Get current tables
    const currentTables = await this.getCurrentTables();
    console.log(`📊 Current tables (${currentTables.length}):`);
    currentTables.forEach((table, index) => {
      console.log(`${index + 1}. ${table}`);
    });

    // Check for critical missing tables
    await this.checkCriticalTables(currentTables);

    // Check notification table structure
    await this.checkNotificationTable();

    // Suggest fixes
    await this.suggestFixes(currentTables);

    await this.connection.end();
  }

  async getCurrentTables() {
    const [rows] = await this.connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ?
      ORDER BY table_name
    `, [this.dbConfig.database]);

    return rows.map(row => row.table_name || row.TABLE_NAME);
  }

  async checkCriticalTables(currentTables) {
    console.log('\n🔍 CHECKING CRITICAL TABLES\n');
    console.log('=' .repeat(35));

    const criticalTables = {
      'users': 'User accounts and authentication',
      'pets': 'Pet information',
      'service_bookings': 'Service bookings and appointments',
      'businesses': 'Business/cremation center information',
      'otp_codes': 'OTP verification codes',
      'otp_attempts': 'OTP attempt tracking',
      'notifications': 'User notifications (or unified_notifications)',
      'admin_notifications': 'Admin notifications (or unified_notifications)',
      'unified_notifications': 'Unified notification system'
    };

    const missingCritical = [];
    const presentCritical = [];

    for (const [table, description] of Object.entries(criticalTables)) {
      if (currentTables.includes(table)) {
        presentCritical.push({ table, description });
        console.log(`✅ ${table} - ${description}`);
      } else {
        missingCritical.push({ table, description });
        console.log(`❌ ${table} - ${description} (MISSING)`);
      }
    }

    // Special check for notification tables
    const hasNotifications = currentTables.includes('notifications');
    const hasAdminNotifications = currentTables.includes('admin_notifications');
    const hasUnifiedNotifications = currentTables.includes('unified_notifications');

    console.log('\n📋 NOTIFICATION TABLES STATUS:');
    if (hasUnifiedNotifications) {
      console.log('✅ unified_notifications exists');
      if (hasNotifications || hasAdminNotifications) {
        console.log('⚠️  Old notification tables still exist - consolidation incomplete');
      }
    } else if (hasNotifications || hasAdminNotifications) {
      console.log('⚠️  Using old notification structure');
    } else {
      console.log('❌ NO notification tables found!');
    }

    return { missingCritical, presentCritical };
  }

  async checkNotificationTable() {
    console.log('\n🔔 NOTIFICATION TABLE ANALYSIS\n');
    console.log('=' .repeat(35));

    try {
      // Check if unified_notifications exists and its structure
      const [tableExists] = await this.connection.execute(`
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = ? AND table_name = 'unified_notifications'
      `, [this.dbConfig.database]);

      if (tableExists[0].count > 0) {
        // Get table structure
        const [columns] = await this.connection.execute(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns
          WHERE table_schema = ? AND table_name = 'unified_notifications'
          ORDER BY ordinal_position
        `, [this.dbConfig.database]);

        console.log('📋 unified_notifications structure:');
        columns.forEach(col => {
          const name = col.column_name || col.COLUMN_NAME;
          const type = col.data_type || col.DATA_TYPE;
          const nullable = col.is_nullable || col.IS_NULLABLE;
          console.log(`  - ${name}: ${type} ${nullable === 'NO' ? '(NOT NULL)' : '(NULLABLE)'}`);
        });

        // Get row count
        const [count] = await this.connection.execute('SELECT COUNT(*) as count FROM unified_notifications');
        console.log(`\n📊 Records: ${count[0].count}`);

      } else {
        console.log('❌ unified_notifications table does not exist');
        
        // Check for old notification tables
        const oldTables = ['notifications', 'admin_notifications', 'business_notifications'];
        for (const table of oldTables) {
          const [exists] = await this.connection.execute(`
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = ? AND table_name = ?
          `, [this.dbConfig.database, table]);

          if (exists[0].count > 0) {
            const [count] = await this.connection.execute(`SELECT COUNT(*) as count FROM \`${table}\``);
            console.log(`📋 ${table}: ${count[0].count} records`);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error checking notification tables:', error.message);
    }
  }

  async suggestFixes(currentTables) {
    console.log('\n🔧 SUGGESTED FIXES\n');
    console.log('=' .repeat(20));

    const fixes = [];

    // Check for missing OTP tables
    if (!currentTables.includes('otp_codes')) {
      fixes.push({
        issue: 'Missing otp_codes table',
        fix: 'CREATE TABLE otp_codes for OTP verification',
        sql: `CREATE TABLE otp_codes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          user_type ENUM('user', 'business', 'admin') NOT NULL DEFAULT 'user',
          otp_code VARCHAR(6) NOT NULL,
          purpose ENUM('registration', 'login', 'password_reset', 'phone_verification') NOT NULL,
          expires_at TIMESTAMP NOT NULL,
          used TINYINT(1) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_otp_code (otp_code),
          INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
      });
    }

    if (!currentTables.includes('otp_attempts')) {
      fixes.push({
        issue: 'Missing otp_attempts table',
        fix: 'CREATE TABLE otp_attempts for rate limiting',
        sql: `CREATE TABLE otp_attempts (
          id INT AUTO_INCREMENT PRIMARY KEY,
          identifier VARCHAR(255) NOT NULL,
          attempt_type ENUM('otp_generation', 'otp_verification') NOT NULL,
          attempts INT DEFAULT 1,
          last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          blocked_until TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_identifier (identifier),
          INDEX idx_blocked_until (blocked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
      });
    }

    // Check notification table situation
    const hasUnified = currentTables.includes('unified_notifications');
    const hasOldNotifications = currentTables.includes('notifications') || 
                               currentTables.includes('admin_notifications');

    if (!hasUnified && !hasOldNotifications) {
      fixes.push({
        issue: 'No notification tables exist',
        fix: 'CREATE notifications table',
        sql: `CREATE TABLE notifications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
          link VARCHAR(255) NULL,
          is_read TINYINT(1) DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_created_at (created_at),
          INDEX idx_is_read (is_read)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
      });
    }

    if (fixes.length === 0) {
      console.log('✅ No critical issues found');
    } else {
      console.log(`⚠️  Found ${fixes.length} issues that need fixing:\n`);
      fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.issue}`);
        console.log(`   Fix: ${fix.fix}\n`);
      });

      // Save SQL fixes to file
      const sqlContent = fixes.map(fix => `-- ${fix.issue}\n-- ${fix.fix}\n${fix.sql};\n`).join('\n');
      await fs.writeFile(
        path.join(this.rootDir || __dirname, '../../../database_fixes.sql'),
        sqlContent,
        'utf8'
      );
      console.log('💾 SQL fixes saved to: database_fixes.sql');
    }
  }
}

// Run the checker
if (require.main === module) {
  const checker = new DatabaseStateChecker();
  checker.connect()
    .then(() => checker.checkDatabaseState())
    .then(() => {
      console.log('\n🎉 Database state check completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

module.exports = DatabaseStateChecker;
