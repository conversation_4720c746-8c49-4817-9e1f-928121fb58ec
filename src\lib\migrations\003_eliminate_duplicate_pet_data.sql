-- Migration 003: Eliminate Duplicate Pet Data Storage
-- Objective: Remove duplicate pet information from service_bookings table
-- Date: 2025-07-14
-- Author: Database Consolidation Project

-- ============================================================================
-- FORWARD MIGRATION
-- ============================================================================

-- Step 1: Create backup tables for safety
CREATE TABLE service_bookings_backup_003 AS SELECT * FROM service_bookings;
CREATE TABLE pets_backup_003 AS SELECT * FROM pets;

-- Step 2: Add pet_id foreign key column to service_bookings
ALTER TABLE service_bookings 
ADD COLUMN pet_id INT NULL 
AFTER package_id,
ADD INDEX idx_service_bookings_pet_id (pet_id);

-- Step 3: Create pets records for bookings that don't have existing pets
-- This handles cases where pet data exists in bookings but not in pets table
INSERT INTO pets (user_id, name, species, breed, photo_path, created_at, updated_at)
SELECT DISTINCT 
  sb.user_id,
  COALESCE(NULLIF(TRIM(sb.pet_name), ''), 'Unknown Pet') as name,
  COALESCE(NULLIF(TRIM(sb.pet_type), ''), 'Unknown') as species,
  'Mixed Breed' as breed,
  sb.pet_image_url as photo_path,
  sb.created_at,
  sb.updated_at
FROM service_bookings sb
LEFT JOIN pets p ON p.user_id = sb.user_id 
  AND LOWER(TRIM(p.name)) = LOWER(TRIM(sb.pet_name))
WHERE p.pet_id IS NULL 
  AND sb.pet_name IS NOT NULL 
  AND TRIM(sb.pet_name) != ''
  AND sb.user_id IS NOT NULL;

-- Step 4: Link existing bookings to pet records
-- First, try exact name matches
UPDATE service_bookings sb
JOIN pets p ON p.user_id = sb.user_id 
  AND LOWER(TRIM(p.name)) = LOWER(TRIM(sb.pet_name))
SET sb.pet_id = p.pet_id
WHERE sb.pet_id IS NULL
  AND sb.pet_name IS NOT NULL
  AND TRIM(sb.pet_name) != '';

-- Step 5: Handle bookings with NULL or empty pet names
-- Link to a default "Unknown Pet" record for each user
INSERT IGNORE INTO pets (user_id, name, species, breed, created_at, updated_at)
SELECT DISTINCT 
  sb.user_id,
  'Unknown Pet' as name,
  'Unknown' as species,
  'Unknown' as breed,
  NOW() as created_at,
  NOW() as updated_at
FROM service_bookings sb
WHERE sb.pet_id IS NULL
  AND (sb.pet_name IS NULL OR TRIM(sb.pet_name) = '')
  AND sb.user_id IS NOT NULL;

-- Link these bookings to the "Unknown Pet" records
UPDATE service_bookings sb
JOIN pets p ON p.user_id = sb.user_id 
  AND p.name = 'Unknown Pet'
SET sb.pet_id = p.pet_id
WHERE sb.pet_id IS NULL
  AND (sb.pet_name IS NULL OR TRIM(sb.pet_name) = '');

-- Step 6: Validation - Check for unlinked bookings
SELECT 
  'Validation Check' as check_type,
  COUNT(*) as total_bookings,
  COUNT(pet_id) as bookings_with_pets,
  COUNT(*) - COUNT(pet_id) as unlinked_bookings
FROM service_bookings;

-- Step 7: If there are still unlinked bookings, create emergency pets
INSERT INTO pets (user_id, name, species, breed, created_at, updated_at)
SELECT DISTINCT 
  sb.user_id,
  CONCAT('Emergency Pet ', sb.id) as name,
  'Unknown' as species,
  'Unknown' as breed,
  NOW() as created_at,
  NOW() as updated_at
FROM service_bookings sb
WHERE sb.pet_id IS NULL
  AND sb.user_id IS NOT NULL;

-- Link emergency pets
UPDATE service_bookings sb
JOIN pets p ON p.user_id = sb.user_id 
  AND p.name = CONCAT('Emergency Pet ', sb.id)
SET sb.pet_id = p.pet_id
WHERE sb.pet_id IS NULL;

-- Step 8: Add foreign key constraint
ALTER TABLE service_bookings 
ADD CONSTRAINT fk_service_bookings_pet_id 
FOREIGN KEY (pet_id) REFERENCES pets(pet_id) 
ON DELETE SET NULL
ON UPDATE CASCADE;

-- Step 9: Final validation before removing columns
SELECT 
  'Final Validation' as validation_type,
  COUNT(*) as total_bookings,
  COUNT(pet_id) as linked_bookings,
  COUNT(*) - COUNT(pet_id) as still_unlinked
FROM service_bookings;

-- Step 10: Remove duplicate columns (only if all bookings are linked)
-- Check if we can safely remove the columns
SET @unlinked_count = (SELECT COUNT(*) FROM service_bookings WHERE pet_id IS NULL);

-- Only proceed if all bookings are linked
DROP PROCEDURE IF EXISTS RemoveDuplicateColumns;
DELIMITER //
CREATE PROCEDURE RemoveDuplicateColumns()
BEGIN
  DECLARE unlinked_bookings INT DEFAULT 0;
  
  SELECT COUNT(*) INTO unlinked_bookings 
  FROM service_bookings 
  WHERE pet_id IS NULL;
  
  IF unlinked_bookings = 0 THEN
    ALTER TABLE service_bookings 
    DROP COLUMN pet_name,
    DROP COLUMN pet_type,
    DROP COLUMN pet_image_url;
    
    SELECT 'SUCCESS: Duplicate pet columns removed' as result;
  ELSE
    SELECT CONCAT('ERROR: Cannot remove columns. ', unlinked_bookings, ' bookings still unlinked') as result;
  END IF;
END //
DELIMITER ;

CALL RemoveDuplicateColumns();
DROP PROCEDURE RemoveDuplicateColumns;

-- Step 11: Update migration history
INSERT INTO migration_history (migration_name, executed_at, success, error_message) 
VALUES ('003_eliminate_duplicate_pet_data', NOW(), 1, NULL);

-- ============================================================================
-- VALIDATION QUERIES
-- ============================================================================

-- Pet data distribution report
SELECT 
  'Pet Data Distribution' as report,
  p.species,
  COUNT(*) as pet_count,
  COUNT(sb.id) as booking_count
FROM pets p
LEFT JOIN service_bookings sb ON sb.pet_id = p.pet_id
GROUP BY p.species
ORDER BY pet_count DESC;

-- User pet ownership report
SELECT 
  'User Pet Ownership' as report,
  u.role,
  COUNT(DISTINCT p.pet_id) as total_pets,
  COUNT(DISTINCT sb.id) as total_bookings
FROM users u
LEFT JOIN pets p ON p.user_id = u.user_id
LEFT JOIN service_bookings sb ON sb.pet_id = p.pet_id
GROUP BY u.role;

-- ============================================================================
-- ROLLBACK SCRIPT (Run only if migration needs to be reversed)
-- ============================================================================

/*
-- ROLLBACK INSTRUCTIONS:
-- 1. Uncomment the following section
-- 2. Execute the rollback script
-- 3. Verify data integrity

-- Rollback Step 1: Add back duplicate columns
ALTER TABLE service_bookings 
ADD COLUMN pet_name VARCHAR(255) NULL AFTER package_id,
ADD COLUMN pet_type VARCHAR(100) NULL AFTER pet_name,
ADD COLUMN pet_image_url VARCHAR(255) NULL AFTER pet_type;

-- Rollback Step 2: Restore data from pets table
UPDATE service_bookings sb
JOIN pets p ON p.pet_id = sb.pet_id
SET 
  sb.pet_name = CASE WHEN p.name = 'Unknown Pet' THEN NULL ELSE p.name END,
  sb.pet_type = CASE WHEN p.species = 'Unknown' THEN NULL ELSE p.species END,
  sb.pet_image_url = p.photo_path;

-- Rollback Step 3: Remove foreign key constraint and pet_id column
ALTER TABLE service_bookings DROP FOREIGN KEY fk_service_bookings_pet_id;
ALTER TABLE service_bookings DROP INDEX idx_service_bookings_pet_id;
ALTER TABLE service_bookings DROP COLUMN pet_id;

-- Rollback Step 4: Remove created pets (optional - be careful!)
-- DELETE FROM pets WHERE name LIKE 'Emergency Pet %' OR name = 'Unknown Pet';

-- Rollback Step 5: Restore from backup
-- TRUNCATE TABLE service_bookings;
-- INSERT INTO service_bookings SELECT * FROM service_bookings_backup_003;
-- TRUNCATE TABLE pets;
-- INSERT INTO pets SELECT * FROM pets_backup_003;

-- Rollback Step 6: Update migration history
UPDATE migration_history 
SET success = 0, error_message = 'Migration rolled back manually'
WHERE migration_name = '003_eliminate_duplicate_pet_data';
*/

-- ============================================================================
-- CLEANUP (Run after successful migration and testing)
-- ============================================================================

-- Drop backup tables after confirming migration success
-- Uncomment the following lines after thorough testing:
-- DROP TABLE service_bookings_backup_003;
-- DROP TABLE pets_backup_003;
