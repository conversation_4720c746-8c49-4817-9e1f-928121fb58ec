-- Migration 002: Standardize Table Naming Convention
-- Objective: Ensure consistent use of service_bookings table name and optimize indexes
-- Date: 2025-07-14
-- Author: Database Consolidation Project

-- ============================================================================
-- FORWARD MIGRATION
-- ============================================================================

-- Step 1: Verify service_bookings table exists and has correct structure
SELECT 
  'Table Verification' as check_type,
  COUNT(*) as booking_count,
  MIN(created_at) as earliest_booking,
  MAX(created_at) as latest_booking
FROM service_bookings;

-- Step 2: Add performance indexes for common query patterns
-- Index for user and provider lookups
CREATE INDEX IF NOT EXISTS idx_service_bookings_user_provider 
ON service_bookings(user_id, provider_id);

-- Index for status and date filtering (dashboard queries)
CREATE INDEX IF NOT EXISTS idx_service_bookings_status_date 
ON service_bookings(status, booking_date);

-- Index for provider dashboard queries
CREATE INDEX IF NOT EXISTS idx_service_bookings_provider_status_date 
ON service_bookings(provider_id, status, booking_date);

-- Index for user booking history
CREATE INDEX IF NOT EXISTS idx_service_bookings_user_date 
ON service_bookings(user_id, booking_date DESC);

-- Index for payment status queries
CREATE INDEX IF NOT EXISTS idx_service_bookings_payment_status 
ON service_bookings(payment_status, status);

-- Step 3: Optimize existing indexes
-- Check if we have any redundant indexes
SELECT 
  'Index Analysis' as analysis_type,
  table_name,
  index_name,
  column_name,
  seq_in_index
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'service_bookings'
ORDER BY index_name, seq_in_index;

-- Step 4: Add composite index for complex dashboard queries
CREATE INDEX IF NOT EXISTS idx_service_bookings_complex_dashboard 
ON service_bookings(provider_id, status, booking_date, payment_status);

-- Step 5: Ensure proper foreign key indexes exist
-- These help with JOIN performance
CREATE INDEX IF NOT EXISTS idx_service_bookings_package_id 
ON service_bookings(package_id);

-- Step 6: Add index for refund-related queries
CREATE INDEX IF NOT EXISTS idx_service_bookings_refund 
ON service_bookings(refund_id, payment_status);

-- Step 7: Verify table structure is optimal
SHOW CREATE TABLE service_bookings;

-- Step 8: Update migration history
INSERT INTO migration_history (migration_name, executed_at, success, error_message) 
VALUES ('002_standardize_table_naming', NOW(), 1, NULL);

-- ============================================================================
-- VALIDATION QUERIES
-- ============================================================================

-- Performance analysis - check index usage
SELECT 
  'Index Usage Analysis' as report_type,
  table_name,
  index_name,
  cardinality,
  CASE 
    WHEN cardinality = 0 THEN 'Unused'
    WHEN cardinality < 10 THEN 'Low Usage'
    WHEN cardinality < 100 THEN 'Medium Usage'
    ELSE 'High Usage'
  END as usage_level
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'service_bookings'
  AND index_name != 'PRIMARY'
ORDER BY cardinality DESC;

-- Query performance test
EXPLAIN SELECT 
  sb.id,
  sb.status,
  sb.booking_date,
  u.first_name,
  u.last_name,
  sp.name as provider_name
FROM service_bookings sb
JOIN users u ON u.user_id = sb.user_id
JOIN service_providers sp ON sp.provider_id = sb.provider_id
WHERE sb.status = 'confirmed'
  AND sb.booking_date >= CURDATE()
ORDER BY sb.booking_date
LIMIT 10;

-- Table size and performance metrics
SELECT 
  'Table Metrics' as metric_type,
  table_name,
  table_rows,
  ROUND(data_length / 1024 / 1024, 2) as data_size_mb,
  ROUND(index_length / 1024 / 1024, 2) as index_size_mb,
  ROUND((data_length + index_length) / 1024 / 1024, 2) as total_size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name = 'service_bookings';

-- ============================================================================
-- CODE REFERENCE UPDATES NEEDED
-- ============================================================================

/*
The following code files need to be updated to ensure consistent table naming:

1. API Routes that check for both 'bookings' and 'service_bookings':
   - src/app/api/admin/dashboard-stats/route.ts (line 75-85)
   - src/app/api/cremation/dashboard/route.ts (line 30-38)

2. Database health checks:
   - Remove fallback checks for 'bookings' table
   - Standardize on 'service_bookings' only

3. Query patterns to update:
   - Replace any remaining references to 'bookings' table
   - Ensure all queries use 'service_bookings'

4. Documentation updates:
   - Update API documentation
   - Update database schema documentation
   - Update README.md if needed
*/

-- ============================================================================
-- ROLLBACK SCRIPT (Run only if migration needs to be reversed)
-- ============================================================================

/*
-- ROLLBACK INSTRUCTIONS:
-- This migration primarily adds indexes, so rollback involves removing them

-- Remove added indexes
DROP INDEX IF EXISTS idx_service_bookings_user_provider ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_status_date ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_provider_status_date ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_user_date ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_payment_status ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_complex_dashboard ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_package_id ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_refund ON service_bookings;

-- Update migration history
UPDATE migration_history 
SET success = 0, error_message = 'Migration rolled back manually'
WHERE migration_name = '002_standardize_table_naming';
*/

-- ============================================================================
-- PERFORMANCE RECOMMENDATIONS
-- ============================================================================

-- Monitor these queries for performance after migration:
-- 1. Dashboard loading queries
-- 2. User booking history
-- 3. Provider booking management
-- 4. Admin reporting queries
-- 5. Payment status updates

-- Consider adding these indexes if specific performance issues arise:
-- CREATE INDEX idx_service_bookings_created_at ON service_bookings(created_at);
-- CREATE INDEX idx_service_bookings_updated_at ON service_bookings(updated_at);

SELECT 'Migration 002 completed successfully' as status;
