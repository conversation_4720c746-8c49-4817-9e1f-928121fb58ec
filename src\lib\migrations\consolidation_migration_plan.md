# Database Consolidation Migration Plan

## Overview
This document outlines the step-by-step migration plan for consolidating the Rainbow Paws database schema and code architecture.

## Pre-Migration Checklist
- [ ] Create full database backup
- [ ] Test migrations on development environment
- [ ] Verify all API endpoints are documented
- [ ] Ensure rollback scripts are tested
- [ ] Schedule maintenance window

## Migration Scripts

### Migration 1: Consolidate User Status Fields
**File**: `001_consolidate_user_status.sql`
**Objective**: Merge `status` and `restriction_status` columns in users table

#### Forward Migration:
```sql
-- Step 1: Add new consolidated status column
ALTER TABLE users ADD COLUMN new_status ENUM(
  'active',
  'inactive', 
  'suspended',
  'restricted',
  'banned'
) DEFAULT 'active' AFTER restriction_status;

-- Step 2: Migrate existing data with priority logic
UPDATE users SET new_status = 
  CASE 
    WHEN restriction_status = 'restricted' THEN 'restricted'
    WHEN restriction_status = 'suspended' THEN 'suspended'
    WHEN status = 'suspended' THEN 'suspended'
    WHEN status = 'restricted' THEN 'restricted'
    WHEN status = 'inactive' THEN 'inactive'
    ELSE 'active'
  END;

-- Step 3: Verify data migration
SELECT 
  status, 
  restriction_status, 
  new_status, 
  COUNT(*) as count 
FROM users 
GROUP BY status, restriction_status, new_status;

-- Step 4: Drop old columns and rename new column
ALTER TABLE users DROP COLUMN restriction_status;
ALTER TABLE users CHANGE COLUMN new_status status ENUM(
  'active',
  'inactive',
  'suspended', 
  'restricted',
  'banned'
) DEFAULT 'active';
```

#### Rollback Script:
```sql
-- Rollback: Restore original columns
ALTER TABLE users ADD COLUMN restriction_status ENUM('none','restricted','suspended') DEFAULT 'none';
ALTER TABLE users CHANGE COLUMN status old_status ENUM('active','inactive','suspended','restricted') DEFAULT 'active';
ALTER TABLE users ADD COLUMN status ENUM('active','inactive','suspended','restricted') DEFAULT 'active';

-- Restore original data logic
UPDATE users SET 
  status = CASE 
    WHEN old_status IN ('active', 'inactive') THEN old_status
    ELSE 'active'
  END,
  restriction_status = CASE
    WHEN old_status = 'restricted' THEN 'restricted'
    WHEN old_status = 'suspended' THEN 'suspended'
    ELSE 'none'
  END;

ALTER TABLE users DROP COLUMN old_status;
```

### Migration 2: Standardize Table Naming
**File**: `002_standardize_table_naming.sql`
**Objective**: Ensure consistent use of `service_bookings` table name

#### Forward Migration:
```sql
-- Verify service_bookings table exists and has correct structure
SELECT COUNT(*) as booking_count FROM service_bookings;

-- Update any references to 'bookings' in stored procedures or views
-- (This is mainly a code change, minimal database changes needed)

-- Add index for performance if not exists
CREATE INDEX IF NOT EXISTS idx_service_bookings_user_provider 
ON service_bookings(user_id, provider_id);

CREATE INDEX IF NOT EXISTS idx_service_bookings_status_date 
ON service_bookings(status, booking_date);
```

### Migration 3: Eliminate Duplicate Pet Data
**File**: `003_eliminate_duplicate_pet_data.sql`
**Objective**: Remove duplicate pet information from service_bookings

#### Forward Migration:
```sql
-- Step 1: Add pet_id foreign key column
ALTER TABLE service_bookings 
ADD COLUMN pet_id INT NULL 
AFTER package_id;

-- Step 2: Create pets records for bookings without existing pets
INSERT INTO pets (user_id, name, species, breed, photo_path, created_at)
SELECT DISTINCT 
  sb.user_id,
  COALESCE(sb.pet_name, 'Unknown Pet') as name,
  COALESCE(sb.pet_type, 'Unknown') as species,
  'Unknown' as breed,
  sb.pet_image_url as photo_path,
  sb.created_at
FROM service_bookings sb
LEFT JOIN pets p ON p.user_id = sb.user_id AND p.name = sb.pet_name
WHERE p.pet_id IS NULL 
AND sb.pet_name IS NOT NULL;

-- Step 3: Link existing bookings to pet records
UPDATE service_bookings sb
JOIN pets p ON p.user_id = sb.user_id 
  AND (p.name = sb.pet_name OR (p.name IS NULL AND sb.pet_name IS NULL))
SET sb.pet_id = p.pet_id
WHERE sb.pet_id IS NULL;

-- Step 4: Verify all bookings have pet_id
SELECT COUNT(*) as unlinked_bookings 
FROM service_bookings 
WHERE pet_id IS NULL;

-- Step 5: Add foreign key constraint
ALTER TABLE service_bookings 
ADD CONSTRAINT fk_service_bookings_pet_id 
FOREIGN KEY (pet_id) REFERENCES pets(pet_id) 
ON DELETE SET NULL;

-- Step 6: Remove duplicate columns
ALTER TABLE service_bookings 
DROP COLUMN pet_name,
DROP COLUMN pet_type,
DROP COLUMN pet_image_url;
```

#### Rollback Script:
```sql
-- Restore duplicate columns
ALTER TABLE service_bookings 
ADD COLUMN pet_name VARCHAR(255) NULL,
ADD COLUMN pet_type VARCHAR(100) NULL,
ADD COLUMN pet_image_url VARCHAR(255) NULL;

-- Restore data from pets table
UPDATE service_bookings sb
JOIN pets p ON p.pet_id = sb.pet_id
SET 
  sb.pet_name = p.name,
  sb.pet_type = p.species,
  sb.pet_image_url = p.photo_path;

-- Remove foreign key and pet_id column
ALTER TABLE service_bookings DROP FOREIGN KEY fk_service_bookings_pet_id;
ALTER TABLE service_bookings DROP COLUMN pet_id;
```

### Migration 4: Database Relationship Optimization
**File**: `004_optimize_relationships.sql`
**Objective**: Add missing foreign keys and optimize indexes

#### Forward Migration:
```sql
-- Add missing foreign key constraints
ALTER TABLE admin_logs 
ADD CONSTRAINT fk_admin_logs_admin_id 
FOREIGN KEY (admin_id) REFERENCES users(user_id) 
ON DELETE CASCADE;

ALTER TABLE business_notifications 
ADD CONSTRAINT fk_business_notifications_user_id 
FOREIGN KEY (user_id) REFERENCES users(user_id) 
ON DELETE CASCADE;

ALTER TABLE reviews 
ADD CONSTRAINT fk_reviews_booking_id 
FOREIGN KEY (booking_id) REFERENCES service_bookings(id) 
ON DELETE CASCADE;

-- Optimize indexes for common queries
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_service_bookings_provider_status ON service_bookings(provider_id, status);
CREATE INDEX idx_payment_transactions_booking_status ON payment_transactions(booking_id, status);

-- Add composite indexes for dashboard queries
CREATE INDEX idx_users_role_status ON users(role, status);
CREATE INDEX idx_service_providers_status_type ON service_providers(application_status, provider_type);
```

## Data Validation Checkpoints

### Checkpoint 1: User Status Migration Validation
```sql
-- Verify no data loss in user status migration
SELECT 
  'Before Migration' as phase,
  status,
  restriction_status,
  COUNT(*) as count
FROM users_backup 
GROUP BY status, restriction_status
UNION ALL
SELECT 
  'After Migration' as phase,
  status,
  'N/A' as restriction_status,
  COUNT(*) as count
FROM users 
GROUP BY status;
```

### Checkpoint 2: Pet Data Migration Validation
```sql
-- Verify all bookings have valid pet references
SELECT 
  COUNT(*) as total_bookings,
  COUNT(pet_id) as bookings_with_pets,
  COUNT(*) - COUNT(pet_id) as orphaned_bookings
FROM service_bookings;

-- Verify pet data integrity
SELECT 
  p.pet_id,
  p.name,
  COUNT(sb.id) as booking_count
FROM pets p
LEFT JOIN service_bookings sb ON sb.pet_id = p.pet_id
GROUP BY p.pet_id, p.name
HAVING booking_count = 0;
```

## Rollback Strategy

### Emergency Rollback Procedure
1. **Stop Application**: Immediately stop the application to prevent data corruption
2. **Restore Database**: Restore from pre-migration backup
3. **Verify Integrity**: Run data integrity checks
4. **Restart Application**: Restart with previous codebase
5. **Investigate**: Analyze migration failure and plan fixes

### Partial Rollback Options
- Each migration script includes individual rollback procedures
- Migrations can be rolled back in reverse order
- Data validation scripts help identify issues before full rollback

## Post-Migration Tasks

### Code Updates Required
1. Update all API routes to use new status enum values
2. Remove references to old `restriction_status` column
3. Update pet data access to use foreign key relationships
4. Update notification services to use consolidated architecture

### Testing Checklist
- [ ] User authentication and authorization
- [ ] Pet profile management
- [ ] Booking creation and management
- [ ] Notification delivery
- [ ] Admin dashboard functionality
- [ ] Business provider features

### Performance Monitoring
- Monitor query performance after index changes
- Check connection pool utilization
- Verify notification delivery times
- Monitor API response times

## Timeline Estimate
- **Migration 1**: 30 minutes
- **Migration 2**: 15 minutes  
- **Migration 3**: 45 minutes
- **Migration 4**: 30 minutes
- **Validation & Testing**: 60 minutes
- **Total Estimated Time**: 3 hours

## Risk Assessment
- **Low Risk**: Table naming standardization
- **Medium Risk**: User status consolidation
- **High Risk**: Pet data elimination (complex data relationships)
- **Mitigation**: Comprehensive testing and validated rollback procedures
