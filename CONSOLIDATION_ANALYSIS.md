# Database & Code Consolidation Analysis

## Database Schema Consolidation Opportunities

### 1. Redundant Status Fields in Users Table
**Issue**: The `users` table has overlapping status management:
- `status` enum('active','inactive','suspended','restricted')
- `restriction_status` enum('none','restricted','suspended')

**Consolidation Plan**: 
- Merge into single `status` field with comprehensive enum
- Create migration script to preserve existing data
- Update all references throughout codebase

### 2. Inconsistent Table Naming
**Issue**: Mixed naming conventions:
- `service_bookings` vs references to `bookings` in code
- Some API routes check for both table names

**Consolidation Plan**:
- Standardize on `service_bookings` as primary table name
- Update all API routes and queries to use consistent naming
- Remove fallback checks for `bookings` table

### 3. Duplicate Pet Data Storage
**Issue**: Pet information stored redundantly:
- Normalized in `pets` table (pet_id, user_id, name, species, breed, etc.)
- Duplicated in `service_bookings` (pet_name, pet_type, pet_image_url)

**Consolidation Plan**:
- Add `pet_id` foreign key to `service_bookings`
- Remove duplicate pet columns from `service_bookings`
- Update booking logic to reference `pets` table
- Create migration to link existing bookings to pet records

### 4. Database Relationship Optimization
**Current Issues**:
- Missing foreign key constraints in some relationships
- Inconsistent indexing patterns
- Some tables lack proper referential integrity

**Consolidation Plan**:
- Add missing foreign key constraints
- Standardize indexing strategy
- Ensure referential integrity across all tables

## Code Service Consolidation Opportunities

### 1. Notification Services Unification
**Current State**: 4 separate notification services with overlapping functionality:

#### A. `notificationService.ts` (General)
- `createNotification()` - Basic notification creation
- `createNotificationFast()` - Fast notification without email
- Email integration with retry logic
- Table existence checking

#### B. `businessNotificationService.ts` (Business-specific)
- `createBusinessNotification()` - Business user notifications
- Custom email templates for business users
- Business-specific notification preferences

#### C. `adminNotificationService.ts` (Admin-specific)
- `createAdminNotification()` - Admin notifications
- Broadcasts to all admin users
- Admin-specific email templates

#### D. `comprehensiveNotificationService.ts` (Booking-specific)
- `createBookingNotification()` - Booking lifecycle notifications
- SMS integration for booking updates
- Complex notification routing logic

**Unified Architecture Design**:
```typescript
// Unified Notification Service
interface NotificationConfig {
  type: 'user' | 'business' | 'admin' | 'system';
  channels: ('in_app' | 'email' | 'sms')[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  template?: string;
  data?: Record<string, any>;
}

class UnifiedNotificationService {
  async send(recipients: Recipients, config: NotificationConfig): Promise<Result>
  async sendToUser(userId: number, config: NotificationConfig): Promise<Result>
  async sendToBusiness(businessId: number, config: NotificationConfig): Promise<Result>
  async sendToAdmins(config: NotificationConfig): Promise<Result>
  async sendSystemWide(config: NotificationConfig): Promise<Result>
}
```

### 2. Authentication Logic Consolidation
**Current Issues**: Scattered authentication patterns across API routes:

#### Multiple Auth Implementations:
- `getAuthTokenFromRequest()` in utils/auth.ts
- `verifySecureAuth()` in lib/secureAuth.ts
- `parseAuthToken()` async/sync versions
- Direct token parsing in various API routes

#### Database Connection Checks:
- `testConnection()` called in multiple routes
- Redundant connection error handling
- Inconsistent fallback strategies

**Unified Architecture Design**:
```typescript
// Unified Auth Middleware
class AuthMiddleware {
  static async verifyUser(request: NextRequest): Promise<AuthResult>
  static async verifyBusiness(request: NextRequest): Promise<AuthResult>
  static async verifyAdmin(request: NextRequest): Promise<AuthResult>
  static async verifyAny(request: NextRequest): Promise<AuthResult>
}

// Unified Database Service
class DatabaseService {
  static async ensureConnection(): Promise<boolean>
  static async ensureTable(tableName: string, schema: TableSchema): Promise<boolean>
  static async healthCheck(): Promise<HealthStatus>
}
```

### 3. Image Handling Consolidation
**Current Issues**: Multiple image utility functions:
- `getImagePath()` in imageUtils.ts
- `getAllPackageImages()` in imageUtils.ts
- Complex path resolution in api/image/[...path]/route.ts
- Duplicate fallback logic

**Unified Architecture Design**:
```typescript
// Unified Image Service
class ImageService {
  static getPath(imagePath: string, options?: ImageOptions): string
  static getPackageImages(packageId: number): Promise<string[]>
  static validateImage(file: File): ValidationResult
  static processUpload(file: File, type: ImageType): Promise<UploadResult>
}
```

### 4. Validation Logic Consolidation
**Current Issues**: Scattered validation patterns:
- Phone number validation in multiple files
- Email validation duplicated
- Database schema validation repeated
- Input sanitization inconsistent

**Unified Architecture Design**:
```typescript
// Unified Validation Service
class ValidationService {
  static validateEmail(email: string): ValidationResult
  static validatePhone(phone: string, country?: string): ValidationResult
  static validateInput(data: any, schema: ValidationSchema): ValidationResult
  static sanitizeInput(data: any): any
}
```

## Migration Strategy

### Phase 1: Database Consolidation
1. Create backup of current database
2. Generate migration scripts for each consolidation
3. Test migrations on development environment
4. Execute migrations with rollback capability

### Phase 2: Service Unification
1. Create unified service interfaces
2. Implement unified services with backward compatibility
3. Gradually migrate API routes to use unified services
4. Remove old service implementations

### Phase 3: Testing & Verification
1. Comprehensive testing of all consolidated functionality
2. Performance benchmarking
3. Production deployment with monitoring
4. Final cleanup of unused code

## Expected Benefits

### Database Benefits:
- Reduced data redundancy
- Improved data consistency
- Better performance through proper indexing
- Simplified maintenance

### Code Benefits:
- Reduced code duplication (~40% reduction estimated)
- Improved maintainability
- Consistent error handling
- Better testing coverage
- Simplified debugging

### Performance Benefits:
- Fewer database queries
- Optimized connection pooling
- Reduced memory footprint
- Faster build times
