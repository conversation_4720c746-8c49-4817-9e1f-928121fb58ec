-- Migration 004: Database Relationship Optimization
-- Objective: Add missing foreign keys, optimize indexes, and ensure referential integrity
-- Date: 2025-07-14
-- Author: Database Consolidation Project

-- ============================================================================
-- FORWARD MIGRATION
-- ============================================================================

-- Step 1: Create backup for safety
CREATE TABLE migration_004_backup AS 
SELECT 'backup_created' as status, NOW() as created_at;

-- Step 2: Add missing foreign key constraints
-- Note: We'll add them with proper error handling

-- Admin logs foreign key
SET @sql = 'ALTER TABLE admin_logs ADD CONSTRAINT fk_admin_logs_admin_id FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE';
SET @sql_check = 'SELECT COUNT(*) FROM information_schema.key_column_usage WHERE constraint_name = "fk_admin_logs_admin_id"';

-- Check if constraint already exists
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists 
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_admin_logs_admin_id' 
  AND table_schema = DATABASE();

-- Add constraint if it doesn't exist
SET @sql_final = CASE 
  WHEN @constraint_exists = 0 THEN @sql 
  ELSE 'SELECT "fk_admin_logs_admin_id already exists" as status' 
END;

PREPARE stmt FROM @sql_final;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Business notifications foreign key
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists 
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_business_notifications_user_id' 
  AND table_schema = DATABASE();

SET @sql = CASE 
  WHEN @constraint_exists = 0 THEN 'ALTER TABLE business_notifications ADD CONSTRAINT fk_business_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
  ELSE 'SELECT "fk_business_notifications_user_id already exists" as status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Reviews foreign key for booking_id
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists 
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_reviews_booking_id' 
  AND table_schema = DATABASE();

SET @sql = CASE 
  WHEN @constraint_exists = 0 THEN 'ALTER TABLE reviews ADD CONSTRAINT fk_reviews_booking_id FOREIGN KEY (booking_id) REFERENCES service_bookings(id) ON DELETE CASCADE'
  ELSE 'SELECT "fk_reviews_booking_id already exists" as status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Reviews foreign key for user_id
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists 
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_reviews_user_id' 
  AND table_schema = DATABASE();

SET @sql = CASE 
  WHEN @constraint_exists = 0 THEN 'ALTER TABLE reviews ADD CONSTRAINT fk_reviews_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE'
  ELSE 'SELECT "fk_reviews_user_id already exists" as status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Reviews foreign key for service_provider_id
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists 
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_reviews_service_provider_id' 
  AND table_schema = DATABASE();

SET @sql = CASE 
  WHEN @constraint_exists = 0 THEN 'ALTER TABLE reviews ADD CONSTRAINT fk_reviews_service_provider_id FOREIGN KEY (service_provider_id) REFERENCES service_providers(provider_id) ON DELETE CASCADE'
  ELSE 'SELECT "fk_reviews_service_provider_id already exists" as status'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 3: Optimize indexes for common queries
-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type, created_at DESC);

-- Service bookings additional indexes
CREATE INDEX IF NOT EXISTS idx_service_bookings_provider_status ON service_bookings(provider_id, status);
CREATE INDEX IF NOT EXISTS idx_service_bookings_user_status ON service_bookings(user_id, status);

-- Payment transactions indexes
CREATE INDEX IF NOT EXISTS idx_payment_transactions_booking_status ON payment_transactions(booking_id, status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status_created ON payment_transactions(status, created_at);

-- User indexes for dashboard queries
CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status);
CREATE INDEX IF NOT EXISTS idx_users_status_created ON users(status, created_at);

-- Service providers indexes
CREATE INDEX IF NOT EXISTS idx_service_providers_status_type ON service_providers(application_status, provider_type);
CREATE INDEX IF NOT EXISTS idx_service_providers_user_id ON service_providers(user_id);

-- Admin logs indexes
CREATE INDEX IF NOT EXISTS idx_admin_logs_admin_action ON admin_logs(admin_id, action);
CREATE INDEX IF NOT EXISTS idx_admin_logs_entity ON admin_logs(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_created_at ON admin_logs(created_at DESC);

-- OTP codes indexes for cleanup and security
CREATE INDEX IF NOT EXISTS idx_otp_codes_expires_at ON otp_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_otp_codes_user_created ON otp_codes(user_id, created_at);

-- Password reset tokens indexes
CREATE INDEX IF NOT EXISTS idx_password_reset_expires ON password_reset_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_user_used ON password_reset_tokens(user_id, is_used);

-- Step 4: Add composite indexes for complex dashboard queries
CREATE INDEX IF NOT EXISTS idx_service_bookings_dashboard_complex 
ON service_bookings(status, booking_date, provider_id, payment_status);

CREATE INDEX IF NOT EXISTS idx_users_admin_dashboard 
ON users(role, status, created_at);

-- Step 5: Optimize package-related indexes
CREATE INDEX IF NOT EXISTS idx_package_inclusions_package_id ON package_inclusions(package_id);
CREATE INDEX IF NOT EXISTS idx_package_addons_package_id ON package_addons(package_id);
CREATE INDEX IF NOT EXISTS idx_package_images_package_order ON package_images(package_id, display_order);

-- Step 6: Add indexes for notification performance
CREATE INDEX IF NOT EXISTS idx_admin_notifications_read_created ON admin_notifications(is_read, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_business_notifications_user_read ON business_notifications(user_id, is_read);

-- Step 7: Update migration history
INSERT INTO migration_history (migration_name, executed_at, success, error_message) 
VALUES ('004_optimize_relationships', NOW(), 1, NULL);

-- ============================================================================
-- VALIDATION QUERIES
-- ============================================================================

-- Foreign key validation report
SELECT 
  'Foreign Key Report' as report_type,
  table_name,
  constraint_name,
  column_name,
  referenced_table_name,
  referenced_column_name
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
  AND referenced_table_name IS NOT NULL
ORDER BY table_name, constraint_name;

-- Index optimization report
SELECT 
  'Index Report' as report_type,
  table_name,
  index_name,
  column_name,
  cardinality,
  CASE 
    WHEN cardinality = 0 THEN 'No Data'
    WHEN cardinality < 10 THEN 'Low Cardinality'
    WHEN cardinality < 100 THEN 'Medium Cardinality'
    ELSE 'High Cardinality'
  END as cardinality_level
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name IN ('users', 'service_bookings', 'notifications', 'reviews', 'payment_transactions')
  AND index_name != 'PRIMARY'
ORDER BY table_name, cardinality DESC;

-- Referential integrity check
SELECT 
  'Referential Integrity Check' as check_type,
  'service_bookings -> users' as relationship,
  COUNT(*) as total_bookings,
  COUNT(u.user_id) as valid_user_references,
  COUNT(*) - COUNT(u.user_id) as orphaned_records
FROM service_bookings sb
LEFT JOIN users u ON u.user_id = sb.user_id

UNION ALL

SELECT 
  'Referential Integrity Check' as check_type,
  'service_bookings -> service_providers' as relationship,
  COUNT(*) as total_bookings,
  COUNT(sp.provider_id) as valid_provider_references,
  COUNT(*) - COUNT(sp.provider_id) as orphaned_records
FROM service_bookings sb
LEFT JOIN service_providers sp ON sp.provider_id = sb.provider_id

UNION ALL

SELECT 
  'Referential Integrity Check' as check_type,
  'reviews -> service_bookings' as relationship,
  COUNT(*) as total_reviews,
  COUNT(sb.id) as valid_booking_references,
  COUNT(*) - COUNT(sb.id) as orphaned_records
FROM reviews r
LEFT JOIN service_bookings sb ON sb.id = r.booking_id;

-- ============================================================================
-- PERFORMANCE MONITORING QUERIES
-- ============================================================================

-- Query to monitor slow queries after optimization
SELECT 
  'Performance Monitoring Setup' as setup_type,
  'Enable slow query log for monitoring' as recommendation,
  'SET GLOBAL slow_query_log = 1' as command_1,
  'SET GLOBAL long_query_time = 2' as command_2;

-- ============================================================================
-- ROLLBACK SCRIPT (Run only if migration needs to be reversed)
-- ============================================================================

/*
-- ROLLBACK INSTRUCTIONS:
-- Remove foreign key constraints and indexes added in this migration

-- Remove foreign key constraints
ALTER TABLE admin_logs DROP FOREIGN KEY IF EXISTS fk_admin_logs_admin_id;
ALTER TABLE business_notifications DROP FOREIGN KEY IF EXISTS fk_business_notifications_user_id;
ALTER TABLE reviews DROP FOREIGN KEY IF EXISTS fk_reviews_booking_id;
ALTER TABLE reviews DROP FOREIGN KEY IF EXISTS fk_reviews_user_id;
ALTER TABLE reviews DROP FOREIGN KEY IF EXISTS fk_reviews_service_provider_id;

-- Remove indexes
DROP INDEX IF EXISTS idx_notifications_user_read ON notifications;
DROP INDEX IF EXISTS idx_notifications_created_at ON notifications;
DROP INDEX IF EXISTS idx_notifications_type ON notifications;
DROP INDEX IF EXISTS idx_service_bookings_provider_status ON service_bookings;
DROP INDEX IF EXISTS idx_service_bookings_user_status ON service_bookings;
DROP INDEX IF EXISTS idx_payment_transactions_booking_status ON payment_transactions;
DROP INDEX IF EXISTS idx_payment_transactions_status_created ON payment_transactions;
DROP INDEX IF EXISTS idx_users_role_status ON users;
DROP INDEX IF EXISTS idx_users_status_created ON users;
DROP INDEX IF EXISTS idx_service_providers_status_type ON service_providers;
DROP INDEX IF EXISTS idx_service_providers_user_id ON service_providers;
DROP INDEX IF EXISTS idx_admin_logs_admin_action ON admin_logs;
DROP INDEX IF EXISTS idx_admin_logs_entity ON admin_logs;
DROP INDEX IF EXISTS idx_admin_logs_created_at ON admin_logs;
DROP INDEX IF EXISTS idx_otp_codes_expires_at ON otp_codes;
DROP INDEX IF EXISTS idx_otp_codes_user_created ON otp_codes;
DROP INDEX IF EXISTS idx_password_reset_expires ON password_reset_tokens;
DROP INDEX IF EXISTS idx_password_reset_user_used ON password_reset_tokens;
DROP INDEX IF EXISTS idx_service_bookings_dashboard_complex ON service_bookings;
DROP INDEX IF EXISTS idx_users_admin_dashboard ON users;
DROP INDEX IF EXISTS idx_package_inclusions_package_id ON package_inclusions;
DROP INDEX IF EXISTS idx_package_addons_package_id ON package_addons;
DROP INDEX IF EXISTS idx_package_images_package_order ON package_images;
DROP INDEX IF EXISTS idx_admin_notifications_read_created ON admin_notifications;
DROP INDEX IF EXISTS idx_business_notifications_user_read ON business_notifications;

-- Update migration history
UPDATE migration_history 
SET success = 0, error_message = 'Migration rolled back manually'
WHERE migration_name = '004_optimize_relationships';

-- Drop backup
DROP TABLE IF EXISTS migration_004_backup;
*/

-- ============================================================================
-- CLEANUP
-- ============================================================================

-- Drop backup table after successful migration
-- Uncomment after thorough testing:
-- DROP TABLE IF EXISTS migration_004_backup;

SELECT 'Migration 004 completed successfully - Database relationships optimized' as status;
